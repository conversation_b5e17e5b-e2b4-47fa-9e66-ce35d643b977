package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	AI       AIConfig       `yaml:"ai" mapstructure:"ai"`
	Agent    AgentConfig    `yaml:"agent" mapstructure:"agent"`
	UI       UIConfig       `yaml:"ui" mapstructure:"ui"`
	Security SecurityConfig `yaml:"security" mapstructure:"security"`
	Cache    CacheConfig    `yaml:"cache" mapstructure:"cache"`
	Logging  LoggingConfig  `yaml:"logging" mapstructure:"logging"`
}

// AIConfig contains AI provider configurations
type AIConfig struct {
	DefaultProvider string        `yaml:"default_provider" mapstructure:"default_provider"`
	OpenAI          OpenAIConfig  `yaml:"openai" mapstructure:"openai"`
	Gemini          GeminiConfig  `yaml:"gemini" mapstructure:"gemini"`
	Mistral         MistralConfig `yaml:"mistral" mapstructure:"mistral"`
	DeepSeek        DeepSeekConfig `yaml:"deepseek" mapstructure:"deepseek"`
	Groq            GroqConfig    `yaml:"groq" mapstructure:"groq"`
	Together        TogetherConfig `yaml:"together" mapstructure:"together"`
	Claude          ClaudeConfig  `yaml:"claude" mapstructure:"claude"`
}

// OpenAIConfig contains OpenAI-specific configuration
type OpenAIConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
	BaseURL     string  `yaml:"base_url" mapstructure:"base_url"`
}

// GeminiConfig contains Gemini-specific configuration
type GeminiConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// MistralConfig contains Mistral-specific configuration
type MistralConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// DeepSeekConfig contains DeepSeek-specific configuration
type DeepSeekConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// GroqConfig contains Groq-specific configuration
type GroqConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// TogetherConfig contains Together AI-specific configuration
type TogetherConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// ClaudeConfig contains Anthropic Claude-specific configuration
type ClaudeConfig struct {
	APIKey      string  `yaml:"api_key" mapstructure:"api_key"`
	Model       string  `yaml:"model" mapstructure:"model"`
	Temperature float64 `yaml:"temperature" mapstructure:"temperature"`
	MaxTokens   int     `yaml:"max_tokens" mapstructure:"max_tokens"`
}

// AgentConfig contains agent-specific configuration
type AgentConfig struct {
	Name            string `yaml:"name" mapstructure:"name"`
	Version         string `yaml:"version" mapstructure:"version"`
	MaxConcurrency  int    `yaml:"max_concurrency" mapstructure:"max_concurrency"`
	RequestTimeout  int    `yaml:"request_timeout" mapstructure:"request_timeout"`
	RetryAttempts   int    `yaml:"retry_attempts" mapstructure:"retry_attempts"`
	EnableCaching   bool   `yaml:"enable_caching" mapstructure:"enable_caching"`
	EnableLogging   bool   `yaml:"enable_logging" mapstructure:"enable_logging"`
}

// UIConfig contains UI-specific configuration
type UIConfig struct {
	Theme           string `yaml:"theme" mapstructure:"theme"`
	ShowTimestamps  bool   `yaml:"show_timestamps" mapstructure:"show_timestamps"`
	ShowTokenCount  bool   `yaml:"show_token_count" mapstructure:"show_token_count"`
	AutoSave        bool   `yaml:"auto_save" mapstructure:"auto_save"`
	HistoryLimit    int    `yaml:"history_limit" mapstructure:"history_limit"`
}

// SecurityConfig contains security-related configuration
type SecurityConfig struct {
	EncryptAPIKeys    bool     `yaml:"encrypt_api_keys" mapstructure:"encrypt_api_keys"`
	AllowedDomains    []string `yaml:"allowed_domains" mapstructure:"allowed_domains"`
	BlockedCommands   []string `yaml:"blocked_commands" mapstructure:"blocked_commands"`
	EnableSandbox     bool     `yaml:"enable_sandbox" mapstructure:"enable_sandbox"`
}

// CacheConfig contains caching configuration
type CacheConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled"`
	TTL         int    `yaml:"ttl" mapstructure:"ttl"`
	MaxSize     int    `yaml:"max_size" mapstructure:"max_size"`
	StoragePath string `yaml:"storage_path" mapstructure:"storage_path"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"`
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`
}

// Load loads the configuration from file and environment variables
func Load() (*Config, error) {
	// Set default configuration path
	home, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	configPath := filepath.Join(home, ".go-agent.yaml")

	// Initialize viper
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// Set environment variable prefix
	viper.SetEnvPrefix("GO_AGENT")
	viper.AutomaticEnv()

	// Set defaults
	setDefaults()

	// Read configuration file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// Config file not found, create default
			config := Default()
			if err := Save(config); err != nil {
				return nil, fmt.Errorf("failed to create default config: %w", err)
			}
			return config, nil
		}
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Unmarshal configuration
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// Save saves the configuration to file
func Save(config *Config) error {
	home, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get user home directory: %w", err)
	}

	configPath := filepath.Join(home, ".go-agent.yaml")

	// Marshal configuration to YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// Write to file
	if err := os.WriteFile(configPath, data, 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// Default returns a default configuration
func Default() *Config {
	return &Config{
		AI: AIConfig{
			DefaultProvider: "openai",
			OpenAI: OpenAIConfig{
				Model:       "gpt-4-turbo-preview",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			Gemini: GeminiConfig{
				Model:       "gemini-pro",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			Mistral: MistralConfig{
				Model:       "mistral-large-latest",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			DeepSeek: DeepSeekConfig{
				Model:       "deepseek-coder",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			Groq: GroqConfig{
				Model:       "mixtral-8x7b-32768",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			Together: TogetherConfig{
				Model:       "meta-llama/Llama-2-70b-chat-hf",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
			Claude: ClaudeConfig{
				Model:       "claude-3-sonnet-20240229",
				Temperature: 0.7,
				MaxTokens:   4000,
			},
		},
		Agent: AgentConfig{
			Name:           "OpenCode AI Terminal Agent (Go Edition)",
			Version:        "2.0.0",
			MaxConcurrency: 10,
			RequestTimeout: 60,
			RetryAttempts:  3,
			EnableCaching:  true,
			EnableLogging:  true,
		},
		UI: UIConfig{
			Theme:          "dark",
			ShowTimestamps: true,
			ShowTokenCount: true,
			AutoSave:       true,
			HistoryLimit:   1000,
		},
		Security: SecurityConfig{
			EncryptAPIKeys:  true,
			AllowedDomains:  []string{"*"},
			BlockedCommands: []string{"rm -rf", "format", "del /f"},
			EnableSandbox:   false,
		},
		Cache: CacheConfig{
			Enabled:     true,
			TTL:         3600, // 1 hour
			MaxSize:     1000,
			StoragePath: filepath.Join(os.TempDir(), "go-agent-cache"),
		},
		Logging: LoggingConfig{
			Level:      "info",
			File:       "go-agent.log",
			MaxSize:    100, // MB
			MaxBackups: 3,
			MaxAge:     28, // days
		},
	}
}

// setDefaults sets default values for viper
func setDefaults() {
	// AI defaults
	viper.SetDefault("ai.default_provider", "openai")
	viper.SetDefault("ai.openai.model", "gpt-4-turbo-preview")
	viper.SetDefault("ai.openai.temperature", 0.7)
	viper.SetDefault("ai.openai.max_tokens", 4000)

	// Agent defaults
	viper.SetDefault("agent.name", "OpenCode AI Terminal Agent (Go Edition)")
	viper.SetDefault("agent.version", "2.0.0")
	viper.SetDefault("agent.max_concurrency", 10)
	viper.SetDefault("agent.request_timeout", 60)
	viper.SetDefault("agent.retry_attempts", 3)
	viper.SetDefault("agent.enable_caching", true)
	viper.SetDefault("agent.enable_logging", true)

	// UI defaults
	viper.SetDefault("ui.theme", "dark")
	viper.SetDefault("ui.show_timestamps", true)
	viper.SetDefault("ui.show_token_count", true)
	viper.SetDefault("ui.auto_save", true)
	viper.SetDefault("ui.history_limit", 1000)

	// Security defaults
	viper.SetDefault("security.encrypt_api_keys", true)
	viper.SetDefault("security.enable_sandbox", false)

	// Cache defaults
	viper.SetDefault("cache.enabled", true)
	viper.SetDefault("cache.ttl", 3600)
	viper.SetDefault("cache.max_size", 1000)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", "go-agent.log")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 3)
	viper.SetDefault("logging.max_age", 28)
}

// IsConfigured checks if the configuration is properly set up
func IsConfigured() bool {
	config, err := Load()
	if err != nil {
		return false
	}

	// Check if at least one AI provider is configured
	return config.AI.OpenAI.APIKey != "" ||
		config.AI.Gemini.APIKey != "" ||
		config.AI.Mistral.APIKey != "" ||
		config.AI.DeepSeek.APIKey != "" ||
		config.AI.Groq.APIKey != "" ||
		config.AI.Together.APIKey != "" ||
		config.AI.Claude.APIKey != ""
}

// GetConfigPath returns the configuration file path
func GetConfigPath() string {
	home, _ := os.UserHomeDir()
	return filepath.Join(home, ".go-agent.yaml")
}

// ShowConfigMenu displays the interactive configuration menu
func ShowConfigMenu() {
	fmt.Println("\n🔧 Configuration Menu")
	fmt.Println(strings.Repeat("=", 50))

	config, err := Load()
	if err != nil {
		fmt.Printf("❌ Error loading config: %v\n", err)
		return
	}

	for {
		fmt.Println("\n📋 Configuration Options:")
		fmt.Println("1. 🧠 AI Providers")
		fmt.Println("2. ⚙️  Agent Settings")
		fmt.Println("3. 🎨 UI Settings")
		fmt.Println("4. 🔒 Security Settings")
		fmt.Println("5. 💾 Cache Settings")
		fmt.Println("6. 📝 Logging Settings")
		fmt.Println("7. 💾 Save Configuration")
		fmt.Println("8. 🔄 Reset to Defaults")
		fmt.Println("9. ❌ Exit")

		fmt.Print("\nSelect option (1-9): ")
		var choice string
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			configureAIProviders(config)
		case "2":
			configureAgent(config)
		case "3":
			configureUI(config)
		case "4":
			configureSecurity(config)
		case "5":
			configureCache(config)
		case "6":
			configureLogging(config)
		case "7":
			if err := Save(config); err != nil {
				fmt.Printf("❌ Error saving config: %v\n", err)
			} else {
				fmt.Println("✅ Configuration saved successfully!")
			}
		case "8":
			config = Default()
			fmt.Println("✅ Configuration reset to defaults!")
		case "9":
			return
		default:
			fmt.Println("❌ Invalid option. Please try again.")
		}
	}
}

func configureAIProviders(config *Config) {
	fmt.Println("\n🧠 AI Provider Configuration")
	fmt.Println(strings.Repeat("-", 40))

	for {
		fmt.Println("\n📋 AI Providers:")
		fmt.Printf("1. OpenAI (Current: %s)\n", getConfigStatus(config.AI.OpenAI.APIKey))
		fmt.Printf("2. Gemini (Current: %s)\n", getConfigStatus(config.AI.Gemini.APIKey))
		fmt.Printf("3. Mistral (Current: %s)\n", getConfigStatus(config.AI.Mistral.APIKey))
		fmt.Printf("4. DeepSeek (Current: %s)\n", getConfigStatus(config.AI.DeepSeek.APIKey))
		fmt.Printf("5. Groq (Current: %s)\n", getConfigStatus(config.AI.Groq.APIKey))
		fmt.Printf("6. Together AI (Current: %s)\n", getConfigStatus(config.AI.Together.APIKey))
		fmt.Printf("7. Claude (Current: %s)\n", getConfigStatus(config.AI.Claude.APIKey))
		fmt.Printf("8. Set Default Provider (Current: %s)\n", config.AI.DefaultProvider)
		fmt.Println("9. ← Back")

		fmt.Print("\nSelect provider to configure (1-9): ")
		var choice string
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			configureOpenAI(&config.AI.OpenAI)
		case "2":
			configureGemini(&config.AI.Gemini)
		case "3":
			configureMistral(&config.AI.Mistral)
		case "4":
			configureDeepSeek(&config.AI.DeepSeek)
		case "5":
			configureGroq(&config.AI.Groq)
		case "6":
			configureTogether(&config.AI.Together)
		case "7":
			configureClaude(&config.AI.Claude)
		case "8":
			configureDefaultProvider(&config.AI)
		case "9":
			return
		default:
			fmt.Println("❌ Invalid option. Please try again.")
		}
	}
}

func configureOpenAI(config *OpenAIConfig) {
	fmt.Println("\n🤖 OpenAI Configuration")

	fmt.Print("Enter OpenAI API Key (leave empty to skip): ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
	}

	fmt.Printf("Current model: %s\n", config.Model)
	fmt.Print("Enter model (leave empty to keep current): ")
	var model string
	fmt.Scanln(&model)
	if model != "" {
		config.Model = model
	}

	fmt.Println("✅ OpenAI configuration updated!")
}

func getConfigStatus(apiKey string) string {
	if apiKey != "" {
		return "✅ Configured"
	}
	return "❌ Not configured"
}

func configureDefaultProvider(config *AIConfig) {
	fmt.Println("\n🎯 Default Provider Selection")
	fmt.Println("Available providers:")
	fmt.Println("1. openai")
	fmt.Println("2. gemini")
	fmt.Println("3. mistral")
	fmt.Println("4. deepseek")
	fmt.Println("5. groq")
	fmt.Println("6. together")
	fmt.Println("7. claude")

	fmt.Print("Select default provider (1-7): ")
	var choice string
	fmt.Scanln(&choice)

	providers := []string{"openai", "gemini", "mistral", "deepseek", "groq", "together", "claude"}
	if choice >= "1" && choice <= "7" {
		index := int(choice[0] - '1')
		config.DefaultProvider = providers[index]
		fmt.Printf("✅ Default provider set to: %s\n", config.DefaultProvider)
	} else {
		fmt.Println("❌ Invalid selection")
	}
}

// Placeholder functions for other providers
func configureGemini(config *GeminiConfig) {
	fmt.Println("\n🔮 Gemini Configuration")
	fmt.Print("Enter Gemini API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ Gemini configuration updated!")
	}
}

func configureMistral(config *MistralConfig) {
	fmt.Println("\n🌪️ Mistral Configuration")
	fmt.Print("Enter Mistral API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ Mistral configuration updated!")
	}
}

func configureDeepSeek(config *DeepSeekConfig) {
	fmt.Println("\n🔍 DeepSeek Configuration")
	fmt.Print("Enter DeepSeek API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ DeepSeek configuration updated!")
	}
}

func configureGroq(config *GroqConfig) {
	fmt.Println("\n⚡ Groq Configuration")
	fmt.Print("Enter Groq API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ Groq configuration updated!")
	}
}

func configureTogether(config *TogetherConfig) {
	fmt.Println("\n🤝 Together AI Configuration")
	fmt.Print("Enter Together AI API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ Together AI configuration updated!")
	}
}

func configureClaude(config *ClaudeConfig) {
	fmt.Println("\n🎭 Claude Configuration")
	fmt.Print("Enter Claude API Key: ")
	var apiKey string
	fmt.Scanln(&apiKey)
	if apiKey != "" {
		config.APIKey = apiKey
		fmt.Println("✅ Claude configuration updated!")
	}
}

// Placeholder functions for other configuration sections
func configureAgent(config *Config) {
	fmt.Println("\n⚙️ Agent Settings - Coming Soon!")
}

func configureUI(config *Config) {
	fmt.Println("\n🎨 UI Settings - Coming Soon!")
}

func configureSecurity(config *Config) {
	fmt.Println("\n🔒 Security Settings - Coming Soon!")
}

func configureCache(config *Config) {
	fmt.Println("\n💾 Cache Settings - Coming Soon!")
}

func configureLogging(config *Config) {
	fmt.Println("\n📝 Logging Settings - Coming Soon!")
}
