package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"go-agent/internal/agent"
	"go-agent/internal/config"
	"go-agent/internal/providers"
	"go-agent/internal/tools"
	"go-agent/internal/ui"
)

var (
	version = "2.0.0"
	cfgFile string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "go-agent",
	Short: "Ultra-Powerful OpenCode AI Terminal Agent in Go",
	Long: `OpenCode AI Terminal Agent - Go Edition
	
An ultra-powerful AI coding assistant with advanced capabilities:
- 154+ intelligent tools for development workflow
- Multi-provider AI support (OpenAI, Gemini, Mistral, DeepSeek, Groq, Together AI)
- Full-stack project generation and autonomous coding
- Advanced context management and smart caching
- Large codebase support with intelligent chunking
- Rich terminal UI with session management`,
	Run: func(cmd *cobra.Command, args []string) {
		runInteractiveMode()
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initConfig)

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.go-agent.yaml)")
	rootCmd.PersistentFlags().Bool("debug", false, "Enable debug mode")
	rootCmd.PersistentFlags().String("provider", "openai", "AI provider to use (openai, gemini, mistral, deepseek, groq, together)")
	
	// Bind flags to viper
	viper.BindPFlag("debug", rootCmd.PersistentFlags().Lookup("debug"))
	viper.BindPFlag("provider", rootCmd.PersistentFlags().Lookup("provider"))

	// Add subcommands
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(testCmd)
}

// initConfig reads in config file and ENV variables.
func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		viper.AddConfigPath(home)
		viper.SetConfigType("yaml")
		viper.SetConfigName(".go-agent")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err == nil {
		if viper.GetBool("debug") {
			fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
		}
	}
}

var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Manage configuration and API keys",
	Run: func(cmd *cobra.Command, args []string) {
		config.ShowConfigMenu()
	},
}

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("OpenCode AI Terminal Agent (Go Edition) v%s\n", version)
		fmt.Println("Ultra-powerful AI coding assistant with 154+ tools")
		fmt.Println("Built with Go for maximum performance and reliability")
	},
}

var testCmd = &cobra.Command{
	Use:   "test",
	Short: "Run comprehensive system tests",
	Run: func(cmd *cobra.Command, args []string) {
		runSystemTests()
	},
}

func runInteractiveMode() {
	// Initialize the UI
	terminal := ui.NewTerminalUI()
	
	// Initialize the agent
	agentInstance, err := agent.NewAdvancedCodingAgent()
	if err != nil {
		log.Fatalf("Failed to initialize agent: %v", err)
	}

	// Setup signal handling
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\n🔄 Shutting down gracefully...")
		cancel()
		os.Exit(0)
	}()

	// Welcome message
	terminal.ShowWelcome(version)
	
	// Main interactive loop
	scanner := bufio.NewScanner(os.Stdin)
	
	for {
		select {
		case <-ctx.Done():
			return
		default:
			terminal.ShowPrompt()
			
			if !scanner.Scan() {
				break
			}
			
			input := strings.TrimSpace(scanner.Text())
			if input == "" {
				continue
			}
			
			// Handle special commands
			if strings.HasPrefix(input, "/") {
				handleSpecialCommand(input, agentInstance, terminal)
				continue
			}
			
			// Process regular message
			response := agentInstance.ProcessMessage(input)
			terminal.ShowResponse(response)
		}
	}
}

func handleSpecialCommand(input string, agent *agent.AdvancedCodingAgent, terminal *ui.TerminalUI) {
	parts := strings.Fields(input)
	command := parts[0]
	
	switch command {
	case "/help":
		terminal.ShowHelp()
	case "/config":
		config.ShowConfigMenu()
	case "/status":
		status := agent.GetStatus()
		terminal.ShowStatus(status)
	case "/sessions":
		sessions := agent.GetSessions()
		terminal.ShowSessions(sessions)
	case "/clear":
		terminal.Clear()
	case "/exit", "/quit":
		fmt.Println("👋 Goodbye!")
		os.Exit(0)
	case "/version":
		fmt.Printf("OpenCode AI Terminal Agent (Go Edition) v%s\n", version)
	case "/tools":
		tools := agent.GetAvailableTools()
		terminal.ShowTools(tools)
	case "/providers":
		providers := agent.GetAvailableProviders()
		terminal.ShowProviders(providers)
	default:
		terminal.ShowError(fmt.Sprintf("Unknown command: %s. Type /help for available commands.", command))
	}
}

func runSystemTests() {
	fmt.Println("🧪 Running Comprehensive System Tests")
	fmt.Println("=" + strings.Repeat("=", 49))
	
	// Test 1: Configuration
	fmt.Println("\n1. Testing Configuration System...")
	if config.IsConfigured() {
		color.Green("✅ Configuration system working")
	} else {
		color.Yellow("⚠️ Configuration needs setup")
	}
	
	// Test 2: AI Providers
	fmt.Println("\n2. Testing AI Providers...")
	providerManager := providers.NewManager()
	availableProviders := providerManager.GetAvailable()
	fmt.Printf("✅ %d AI providers available: %v\n", len(availableProviders), availableProviders)
	
	// Test 3: Tools System
	fmt.Println("\n3. Testing Tools System...")
	toolManager := tools.NewManager()
	allTools := toolManager.GetAllTools()
	fmt.Printf("✅ %d tools loaded successfully\n", len(allTools))
	
	// Test 4: Agent Initialization
	fmt.Println("\n4. Testing Agent Initialization...")
	agent, err := agent.NewAdvancedCodingAgent()
	if err != nil {
		color.Red("❌ Agent initialization failed: %v", err)
		return
	}
	color.Green("✅ Agent initialized successfully")
	
	// Test 5: Core Functionality
	fmt.Println("\n5. Testing Core Functionality...")
	testResponse := agent.ProcessMessage("test system status")
	if testResponse != "" {
		color.Green("✅ Message processing working")
	} else {
		color.Red("❌ Message processing failed")
	}
	
	fmt.Println("\n🎉 System tests completed!")
	fmt.Printf("🚀 Go Agent is ready with %d tools and %d providers\n", len(allTools), len(availableProviders))
}

func main() {
	Execute()
}
