import logging

# Configure logging for better debugging and monitoring
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def greet_user(user_greeting: str) -> str:
    """
    Responds to the user's greeting with a friendly message.

    Args:
        user_greeting: The greeting message from the user (string).

    Returns:
        A friendly response message (string).  Returns an error message if the input is invalid.

    Raises:
        TypeError: If the input is not a string.
        ValueError: If the input string is empty or contains only whitespace.
    """

    try:
        # Input validation: Check if the input is a string
        if not isinstance(user_greeting, str):
            raise TypeError("Input must be a string.")

        # Input validation: Check if the input string is empty or contains only whitespace
        if not user_greeting.strip():  # strip() removes leading/trailing whitespace
            raise ValueError("Input string cannot be empty or contain only whitespace.")

        # Normalize the greeting to lowercase for easier comparison
        normalized_greeting = user_greeting.lower()

        # Respond based on the greeting
        if "hello" in normalized_greeting or "hi" in normalized_greeting or "hey" in normalized_greeting:
            response = "Hello there! How can I help you today?"
        elif "good morning" in normalized_greeting:
            response = "Good morning! I hope you have a wonderful day."
        elif "good afternoon" in normalized_greeting:
            response = "Good afternoon! What can I do for you?"
        elif "good evening" in normalized_greeting:
            response = "Good evening!  How may I assist you?"
        else:
            response = "Greetings!  How can I be of service?"

        logging.info(f"User greeting: {user_greeting}, Response: {response}")  # Log the interaction
        return response

    except TypeError as e:
        logging.error(f"TypeError: {e}")
        return "Error: Invalid input type. Please provide a string."
    except ValueError as e:
        logging.error(f"ValueError: {e}")
        return "Error: Invalid input. Please provide a non-empty string."
    except Exception as e:
        logging.exception("An unexpected error occurred:")  # Log the full exception traceback
        return "Error: An unexpected error occurred. Please try again later."


if __name__ == '__main__':
    # Example usage
    user_input = input("Enter your greeting: ")
    try:
        response = greet_user(user_input)
        print(response)
    except Exception as e:
        print(f"An error occurred: {e}")
```