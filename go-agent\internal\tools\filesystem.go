package tools

import (
	"bufio"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// CreateFileTool creates a new file with content
type CreateFileTool struct{}

func (t *CreateFileTool) GetName() string        { return "create_file" }
func (t *CreateFileTool) GetDescription() string { return "Create a new file with specified content" }
func (t *CreateFileTool) GetCategory() string    { return "filesystem" }

func (t *CreateFileTool) IsRelevant(query string) bool {
	keywords := []string{"create", "file", "new", "write"}
	return containsAny(query, keywords)
}

func (t *CreateFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	content, _ := args["content"].(string)

	// Create directory if it doesn't exist
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}

	// Create and write file
	if err := ioutil.WriteFile(path, []byte(content), 0644); err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}

	return fmt.Sprintf("✅ File created successfully: %s", path), nil
}

// ReadFileTool reads file content
type ReadFileTool struct{}

func (t *ReadFileTool) GetName() string        { return "read_file" }
func (t *ReadFileTool) GetDescription() string { return "Read content from a file" }
func (t *ReadFileTool) GetCategory() string    { return "filesystem" }

func (t *ReadFileTool) IsRelevant(query string) bool {
	keywords := []string{"read", "file", "content", "show", "display"}
	return containsAny(query, keywords)
}

func (t *ReadFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	content, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Handle line range if specified
	if startLine, ok := args["start_line"].(float64); ok {
		if endLine, ok := args["end_line"].(float64); ok {
			lines := strings.Split(string(content), "\n")
			start := int(startLine) - 1
			end := int(endLine)

			if start < 0 {
				start = 0
			}
			if end > len(lines) {
				end = len(lines)
			}
			if start >= end {
				return "", nil
			}

			selectedLines := lines[start:end]
			return strings.Join(selectedLines, "\n"), nil
		}
	}

	return string(content), nil
}

// WriteFileTool writes content to a file
type WriteFileTool struct{}

func (t *WriteFileTool) GetName() string        { return "write_file" }
func (t *WriteFileTool) GetDescription() string { return "Write content to a file (overwrites existing)" }
func (t *WriteFileTool) GetCategory() string    { return "filesystem" }

func (t *WriteFileTool) IsRelevant(query string) bool {
	keywords := []string{"write", "file", "save", "overwrite"}
	return containsAny(query, keywords)
}

func (t *WriteFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	content, ok := args["content"].(string)
	if !ok {
		return nil, fmt.Errorf("content parameter is required")
	}

	if err := ioutil.WriteFile(path, []byte(content), 0644); err != nil {
		return nil, fmt.Errorf("failed to write file: %w", err)
	}

	return fmt.Sprintf("✅ File written successfully: %s", path), nil
}

// DeleteFileTool deletes a file
type DeleteFileTool struct{}

func (t *DeleteFileTool) GetName() string        { return "delete_file" }
func (t *DeleteFileTool) GetDescription() string { return "Delete a file" }
func (t *DeleteFileTool) GetCategory() string    { return "filesystem" }

func (t *DeleteFileTool) IsRelevant(query string) bool {
	keywords := []string{"delete", "remove", "file"}
	return containsAny(query, keywords)
}

func (t *DeleteFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	if err := os.Remove(path); err != nil {
		return nil, fmt.Errorf("failed to delete file: %w", err)
	}

	return fmt.Sprintf("✅ File deleted successfully: %s", path), nil
}

// MoveFileTool moves a file
type MoveFileTool struct{}

func (t *MoveFileTool) GetName() string        { return "move_file" }
func (t *MoveFileTool) GetDescription() string { return "Move a file to a new location" }
func (t *MoveFileTool) GetCategory() string    { return "filesystem" }

func (t *MoveFileTool) IsRelevant(query string) bool {
	keywords := []string{"move", "file", "relocate"}
	return containsAny(query, keywords)
}

func (t *MoveFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	src, ok := args["src"].(string)
	if !ok {
		return nil, fmt.Errorf("src parameter is required")
	}

	dest, ok := args["dest"].(string)
	if !ok {
		return nil, fmt.Errorf("dest parameter is required")
	}

	// Create destination directory if needed
	destDir := filepath.Dir(dest)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create destination directory: %w", err)
	}

	if err := os.Rename(src, dest); err != nil {
		return nil, fmt.Errorf("failed to move file: %w", err)
	}

	return fmt.Sprintf("✅ File moved successfully: %s → %s", src, dest), nil
}

// CopyFileTool copies a file
type CopyFileTool struct{}

func (t *CopyFileTool) GetName() string        { return "copy_file" }
func (t *CopyFileTool) GetDescription() string { return "Copy a file to a new location" }
func (t *CopyFileTool) GetCategory() string    { return "filesystem" }

func (t *CopyFileTool) IsRelevant(query string) bool {
	keywords := []string{"copy", "file", "duplicate"}
	return containsAny(query, keywords)
}

func (t *CopyFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	src, ok := args["src"].(string)
	if !ok {
		return nil, fmt.Errorf("src parameter is required")
	}

	dest, ok := args["dest"].(string)
	if !ok {
		return nil, fmt.Errorf("dest parameter is required")
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return nil, fmt.Errorf("failed to open source file: %w", err)
	}
	defer srcFile.Close()

	// Create destination directory if needed
	destDir := filepath.Dir(dest)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create destination directory: %w", err)
	}

	// Create destination file
	destFile, err := os.Create(dest)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer destFile.Close()

	// Copy content
	if _, err := io.Copy(destFile, srcFile); err != nil {
		return nil, fmt.Errorf("failed to copy file content: %w", err)
	}

	return fmt.Sprintf("✅ File copied successfully: %s → %s", src, dest), nil
}

// BackupFileTool creates a backup of a file
type BackupFileTool struct{}

func (t *BackupFileTool) GetName() string        { return "backup_file" }
func (t *BackupFileTool) GetDescription() string { return "Create a backup copy of a file" }
func (t *BackupFileTool) GetCategory() string    { return "filesystem" }

func (t *BackupFileTool) IsRelevant(query string) bool {
	keywords := []string{"backup", "file", "save"}
	return containsAny(query, keywords)
}

func (t *BackupFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	// Generate backup filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	ext := filepath.Ext(path)
	base := strings.TrimSuffix(path, ext)
	backupPath := fmt.Sprintf("%s.backup_%s%s", base, timestamp, ext)

	// Copy file to backup location
	copyTool := &CopyFileTool{}
	_, err := copyTool.Execute(map[string]interface{}{
		"src":  path,
		"dest": backupPath,
	})

	if err != nil {
		return nil, err
	}

	return fmt.Sprintf("✅ Backup created: %s", backupPath), nil
}

// AppendToFileTool appends content to a file
type AppendToFileTool struct{}

func (t *AppendToFileTool) GetName() string        { return "append_to_file" }
func (t *AppendToFileTool) GetDescription() string { return "Append content to the end of a file" }
func (t *AppendToFileTool) GetCategory() string    { return "filesystem" }

func (t *AppendToFileTool) IsRelevant(query string) bool {
	keywords := []string{"append", "add", "file", "end"}
	return containsAny(query, keywords)
}

func (t *AppendToFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	content, ok := args["content"].(string)
	if !ok {
		return nil, fmt.Errorf("content parameter is required")
	}

	file, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	if _, err := file.WriteString(content); err != nil {
		return nil, fmt.Errorf("failed to append to file: %w", err)
	}

	return fmt.Sprintf("✅ Content appended to file: %s", path), nil
}

// PrependToFileTool prepends content to a file
type PrependToFileTool struct{}

func (t *PrependToFileTool) GetName() string        { return "prepend_to_file" }
func (t *PrependToFileTool) GetDescription() string { return "Prepend content to the beginning of a file" }
func (t *PrependToFileTool) GetCategory() string    { return "filesystem" }

func (t *PrependToFileTool) IsRelevant(query string) bool {
	keywords := []string{"prepend", "add", "file", "beginning", "start"}
	return containsAny(query, keywords)
}

func (t *PrependToFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	content, ok := args["content"].(string)
	if !ok {
		return nil, fmt.Errorf("content parameter is required")
	}

	// Read existing content
	existingContent, err := ioutil.ReadFile(path)
	if err != nil && !os.IsNotExist(err) {
		return nil, fmt.Errorf("failed to read existing file: %w", err)
	}

	// Combine new content with existing
	newContent := content + string(existingContent)

	// Write back to file
	if err := ioutil.WriteFile(path, []byte(newContent), 0644); err != nil {
		return nil, fmt.Errorf("failed to write file: %w", err)
	}

	return fmt.Sprintf("✅ Content prepended to file: %s", path), nil
}

// Helper function to check if query contains any of the keywords
func containsAny(query string, keywords []string) bool {
	queryLower := strings.ToLower(query)
	for _, keyword := range keywords {
		if strings.Contains(queryLower, keyword) {
			return true
		}
	}
	return false
}

// ListDirectoryTool lists directory contents
type ListDirectoryTool struct{}

func (t *ListDirectoryTool) GetName() string        { return "list_directory" }
func (t *ListDirectoryTool) GetDescription() string { return "List contents of a directory" }
func (t *ListDirectoryTool) GetCategory() string    { return "filesystem" }

func (t *ListDirectoryTool) IsRelevant(query string) bool {
	keywords := []string{"list", "directory", "ls", "dir", "contents"}
	return containsAny(query, keywords)
}

func (t *ListDirectoryTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		path = "." // Default to current directory
	}

	entries, err := os.ReadDir(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory: %w", err)
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("📁 Directory listing for %s:\n\n", path))

	for _, entry := range entries {
		icon := "📄"
		if entry.IsDir() {
			icon = "📁"
		}

		info, err := entry.Info()
		if err != nil {
			continue
		}

		result.WriteString(fmt.Sprintf("%s %s (%d bytes, %s)\n",
			icon, entry.Name(), info.Size(), info.ModTime().Format("2006-01-02 15:04")))
	}

	return result.String(), nil
}

// CreateDirectoryTool creates directories
type CreateDirectoryTool struct{}

func (t *CreateDirectoryTool) GetName() string        { return "create_directory" }
func (t *CreateDirectoryTool) GetDescription() string { return "Create a new directory" }
func (t *CreateDirectoryTool) GetCategory() string    { return "filesystem" }

func (t *CreateDirectoryTool) IsRelevant(query string) bool {
	keywords := []string{"create", "directory", "mkdir", "folder"}
	return containsAny(query, keywords)
}

func (t *CreateDirectoryTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	if err := os.MkdirAll(path, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}

	return fmt.Sprintf("✅ Directory created successfully: %s", path), nil
}

// SearchFilesTool searches for files
type SearchFilesTool struct{}

func (t *SearchFilesTool) GetName() string        { return "search_files" }
func (t *SearchFilesTool) GetDescription() string { return "Search for files by name pattern" }
func (t *SearchFilesTool) GetCategory() string    { return "filesystem" }

func (t *SearchFilesTool) IsRelevant(query string) bool {
	keywords := []string{"search", "find", "files", "pattern"}
	return containsAny(query, keywords)
}

func (t *SearchFilesTool) Execute(args map[string]interface{}) (interface{}, error) {
	pattern, ok := args["pattern"].(string)
	if !ok {
		return nil, fmt.Errorf("pattern parameter is required")
	}

	searchPath, _ := args["path"].(string)
	if searchPath == "" {
		searchPath = "."
	}

	var results []string
	err := filepath.Walk(searchPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Skip errors
		}

		matched, err := filepath.Match(pattern, filepath.Base(path))
		if err != nil {
			return nil
		}

		if matched {
			results = append(results, path)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	if len(results) == 0 {
		return fmt.Sprintf("No files found matching pattern: %s", pattern), nil
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("🔍 Found %d files matching '%s':\n\n", len(results), pattern))

	for _, file := range results {
		result.WriteString(fmt.Sprintf("📄 %s\n", file))
	}

	return result.String(), nil
}

// GrepSearchTool searches within files
type GrepSearchTool struct{}

func (t *GrepSearchTool) GetName() string        { return "grep_search" }
func (t *GrepSearchTool) GetDescription() string { return "Search for text within files" }
func (t *GrepSearchTool) GetCategory() string    { return "filesystem" }

func (t *GrepSearchTool) IsRelevant(query string) bool {
	keywords := []string{"grep", "search", "text", "content", "find"}
	return containsAny(query, keywords)
}

func (t *GrepSearchTool) Execute(args map[string]interface{}) (interface{}, error) {
	searchText, ok := args["text"].(string)
	if !ok {
		return nil, fmt.Errorf("text parameter is required")
	}

	searchPath, _ := args["path"].(string)
	if searchPath == "" {
		searchPath = "."
	}

	var results []string
	err := filepath.Walk(searchPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() {
			return nil
		}

		// Skip binary files
		if !isTextFile(path) {
			return nil
		}

		file, err := os.Open(path)
		if err != nil {
			return nil
		}
		defer file.Close()

		scanner := bufio.NewScanner(file)
		lineNum := 1
		for scanner.Scan() {
			line := scanner.Text()
			if strings.Contains(strings.ToLower(line), strings.ToLower(searchText)) {
				results = append(results, fmt.Sprintf("%s:%d: %s", path, lineNum, line))
			}
			lineNum++
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	if len(results) == 0 {
		return fmt.Sprintf("No matches found for: %s", searchText), nil
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("🔍 Found %d matches for '%s':\n\n", len(results), searchText))

	for _, match := range results {
		result.WriteString(fmt.Sprintf("📄 %s\n", match))
	}

	return result.String(), nil
}

// Helper function to check if file is likely text
func isTextFile(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	textExts := []string{".txt", ".md", ".go", ".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".css", ".html", ".xml", ".json", ".yaml", ".yml"}

	for _, textExt := range textExts {
		if ext == textExt {
			return true
		}
	}

	return false
}

// ReplaceInFileTool replaces text in files
type ReplaceInFileTool struct{}

func (t *ReplaceInFileTool) GetName() string        { return "replace_in_file" }
func (t *ReplaceInFileTool) GetDescription() string { return "Replace text in a file" }
func (t *ReplaceInFileTool) GetCategory() string    { return "filesystem" }

func (t *ReplaceInFileTool) IsRelevant(query string) bool {
	keywords := []string{"replace", "substitute", "change", "text"}
	return containsAny(query, keywords)
}

func (t *ReplaceInFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	oldText, ok := args["old_text"].(string)
	if !ok {
		return nil, fmt.Errorf("old_text parameter is required")
	}

	newText, ok := args["new_text"].(string)
	if !ok {
		return nil, fmt.Errorf("new_text parameter is required")
	}

	// Read file
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Replace text
	originalContent := string(content)
	newContent := strings.ReplaceAll(originalContent, oldText, newText)

	// Count replacements
	oldCount := strings.Count(originalContent, oldText)
	if oldCount == 0 {
		return fmt.Sprintf("⚠️ No occurrences of '%s' found in %s", oldText, path), nil
	}

	// Write back
	if err := ioutil.WriteFile(path, []byte(newContent), 0644); err != nil {
		return nil, fmt.Errorf("failed to write file: %w", err)
	}

	return fmt.Sprintf("✅ Replaced %d occurrences of '%s' with '%s' in %s", oldCount, oldText, newText, path), nil
}

// InsertIntoFileTool inserts text at specific line
type InsertIntoFileTool struct{}

func (t *InsertIntoFileTool) GetName() string        { return "insert_into_file" }
func (t *InsertIntoFileTool) GetDescription() string { return "Insert text at a specific line in a file" }
func (t *InsertIntoFileTool) GetCategory() string    { return "filesystem" }

func (t *InsertIntoFileTool) IsRelevant(query string) bool {
	keywords := []string{"insert", "add", "line", "text"}
	return containsAny(query, keywords)
}

func (t *InsertIntoFileTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, ok := args["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter is required")
	}

	text, ok := args["text"].(string)
	if !ok {
		return nil, fmt.Errorf("text parameter is required")
	}

	lineNum, ok := args["line"].(float64)
	if !ok {
		return nil, fmt.Errorf("line parameter is required")
	}

	// Read file
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	lines := strings.Split(string(content), "\n")
	insertAt := int(lineNum) - 1 // Convert to 0-based index

	if insertAt < 0 {
		insertAt = 0
	}
	if insertAt > len(lines) {
		insertAt = len(lines)
	}

	// Insert text
	newLines := make([]string, 0, len(lines)+1)
	newLines = append(newLines, lines[:insertAt]...)
	newLines = append(newLines, text)
	newLines = append(newLines, lines[insertAt:]...)

	// Write back
	newContent := strings.Join(newLines, "\n")
	if err := ioutil.WriteFile(path, []byte(newContent), 0644); err != nil {
		return nil, fmt.Errorf("failed to write file: %w", err)
	}

	return fmt.Sprintf("✅ Inserted text at line %d in %s", int(lineNum), path), nil
}
