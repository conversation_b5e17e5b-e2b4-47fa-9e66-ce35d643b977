package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go-agent/internal/config"
	"go-agent/internal/providers"
	"go-agent/internal/tools"
	"go-agent/internal/memory"
	"go-agent/internal/context_manager"
	"go-agent/internal/session"
)

// AdvancedCodingAgent represents the main AI coding agent
type AdvancedCodingAgent struct {
	// Core components
	config          *config.Config
	providerManager *providers.Manager
	toolManager     *tools.Manager
	memoryManager   *memory.Manager
	contextManager  *context_manager.Manager
	sessionManager  *session.Manager
	
	// State management
	currentSession  *session.Session
	conversationID  string
	isProcessing    bool
	
	// Performance optimization
	cache           map[string]interface{}
	requestQueue    chan *Request
	responseCache   *ResponseCache
	
	// Concurrency control
	mu              sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
}

// Request represents a user request to the agent
type Request struct {
	ID        string                 `json:"id"`
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id"`
}

// Response represents the agent's response
type Response struct {
	ID        string                 `json:"id"`
	Content   string                 `json:"content"`
	Tools     []string               `json:"tools_used"`
	Context   map[string]interface{} `json:"context"`
	Timestamp time.Time              `json:"timestamp"`
	Success   bool                   `json:"success"`
	Error     string                 `json:"error,omitempty"`
}

// ResponseCache manages response caching for performance
type ResponseCache struct {
	cache map[string]*Response
	mu    sync.RWMutex
	ttl   time.Duration
}

// NewAdvancedCodingAgent creates a new instance of the advanced coding agent
func NewAdvancedCodingAgent() (*AdvancedCodingAgent, error) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())

	// Initialize managers
	providerManager := providers.NewManager()
	toolManager := tools.NewManager()
	memoryManager := memory.NewManager()
	contextManager := context_manager.NewManager()
	sessionManager := session.NewManager()

	// Initialize response cache
	responseCache := &ResponseCache{
		cache: make(map[string]*Response),
		ttl:   time.Hour * 24, // 24 hour cache
	}

	agent := &AdvancedCodingAgent{
		config:          cfg,
		providerManager: providerManager,
		toolManager:     toolManager,
		memoryManager:   memoryManager,
		contextManager:  contextManager,
		sessionManager:  sessionManager,
		cache:           make(map[string]interface{}),
		requestQueue:    make(chan *Request, 100),
		responseCache:   responseCache,
		ctx:             ctx,
		cancel:          cancel,
	}

	// Initialize default session
	defaultSession, err := agent.sessionManager.CreateSession("default")
	if err != nil {
		return nil, fmt.Errorf("failed to create default session: %w", err)
	}
	agent.currentSession = defaultSession

	// Start background workers
	go agent.processRequestQueue()
	go agent.cleanupCache()

	return agent, nil
}

// ProcessMessage processes a user message and returns a response
func (a *AdvancedCodingAgent) ProcessMessage(message string) string {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.isProcessing {
		return "⏳ Agent is currently processing another request. Please wait..."
	}

	a.isProcessing = true
	defer func() { a.isProcessing = false }()

	// Check cache first
	if cached := a.getCachedResponse(message); cached != nil {
		return cached.Content
	}

	// Create request
	request := &Request{
		ID:        generateID(),
		Message:   message,
		Context:   a.contextManager.GetCurrentContext(),
		Timestamp: time.Now(),
		UserID:    "default",
	}

	// Process the request
	response := a.processRequest(request)

	// Cache the response
	a.cacheResponse(message, response)

	// Update context and memory
	a.contextManager.UpdateContext(message, response.Content)
	a.memoryManager.AddInteraction(message, response.Content)

	return response.Content
}

// prepareAIContext prepares context for AI processing
func (a *AdvancedCodingAgent) prepareAIContext(req *Request, tools []tools.Tool) string {
	// Simple context preparation - in a real implementation, this would be more sophisticated
	context := fmt.Sprintf("User request: %s\n", req.Message)

	if len(tools) > 0 {
		context += "Available tools:\n"
		for _, tool := range tools {
			context += fmt.Sprintf("- %s: %s\n", tool.GetName(), tool.GetDescription())
		}
	}

	// Add recent context
	recentContext := a.contextManager.GetCurrentContext()
	if len(recentContext) > 0 {
		context += "\nRecent context:\n"
		for key, value := range recentContext {
			context += fmt.Sprintf("- %s: %v\n", key, value)
		}
	}

	return context
}

// processAIResponse processes the AI response and executes tools if needed
func (a *AdvancedCodingAgent) processAIResponse(aiResponse string, tools []tools.Tool) string {
	// Simple response processing - in a real implementation, this would parse tool calls
	return aiResponse
}

// processRequest handles the actual request processing
func (a *AdvancedCodingAgent) processRequest(req *Request) *Response {
	response := &Response{
		ID:        generateID(),
		Timestamp: time.Now(),
		Success:   true,
	}

	// Handle special commands
	if isSpecialCommand(req.Message) {
		response.Content = a.handleSpecialCommand(req.Message)
		return response
	}

	// Determine which tools to use
	relevantTools := a.toolManager.GetRelevantTools(req.Message)
	
	// Get AI provider
	provider, err := a.providerManager.GetActiveProvider()
	if err != nil {
		response.Success = false
		response.Error = fmt.Sprintf("No AI provider available: %v", err)
		response.Content = "❌ No AI provider configured. Please use /config to set up API keys."
		return response
	}

	// Prepare context for AI
	context := a.prepareAIContext(req, relevantTools)

	// Get AI response
	aiContext := providers.AIContext{
		Messages: []providers.Message{
			{Role: "user", Content: context},
		},
		Temperature: 0.7,
		MaxTokens:   2000,
	}
	aiResponse, err := provider.GenerateResponse(aiContext)
	if err != nil {
		response.Success = false
		response.Error = err.Error()
		response.Content = fmt.Sprintf("❌ AI provider error: %v", err)
		return response
	}

	// Process AI response and execute tools if needed
	finalResponse := a.processAIResponse(aiResponse, relevantTools)
	
	response.Content = finalResponse
	response.Tools = getToolNames(relevantTools)

	return response
}

// handleSpecialCommand processes special commands like /help, /config, etc.
func (a *AdvancedCodingAgent) handleSpecialCommand(command string) string {
	switch command {
	case "/help":
		return a.generateHelpText()
	case "/status":
		return a.generateStatusText()
	case "/tools":
		return a.generateToolsList()
	case "/providers":
		return a.generateProvidersList()
	case "/sessions":
		return fmt.Sprintf("📋 Session Management:\n- Current session: %s\n- Sessions available: 1", a.currentSession)
	case "/clear":
		a.memoryManager.Clear()
		return "🧹 Memory cleared successfully"
	default:
		return fmt.Sprintf("❌ Unknown command: %s. Type /help for available commands.", command)
	}
}

// GetStatus returns the current agent status
func (a *AdvancedCodingAgent) GetStatus() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()

	return map[string]interface{}{
		"version":           "2.0.0",
		"status":            "operational",
		"tools_count":       a.toolManager.GetToolCount(),
		"providers_count":   a.providerManager.GetProviderCount(),
		"current_session":   a.currentSession.ID,
		"memory_size":       a.memoryManager.GetSize(),
		"cache_size":        len(a.cache),
		"is_processing":     a.isProcessing,
		"uptime":           time.Since(a.currentSession.CreatedAt),
	}
}

// GetSessions returns all available sessions
func (a *AdvancedCodingAgent) GetSessions() []*session.Session {
	return a.sessionManager.GetAllSessions()
}

// GetAvailableTools returns all available tools
func (a *AdvancedCodingAgent) GetAvailableTools() []string {
	return a.toolManager.GetAllToolNames()
}

// GetAvailableProviders returns all available AI providers
func (a *AdvancedCodingAgent) GetAvailableProviders() []string {
	return a.providerManager.GetAvailableProviders()
}

// Shutdown gracefully shuts down the agent
func (a *AdvancedCodingAgent) Shutdown() error {
	a.cancel()
	
	// Save session state
	if err := a.sessionManager.SaveSession(a.currentSession); err != nil {
		return fmt.Errorf("failed to save session: %w", err)
	}
	
	// Close resources
	close(a.requestQueue)
	
	return nil
}

// Helper functions

func generateID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

func isSpecialCommand(message string) bool {
	return len(message) > 0 && message[0] == '/'
}

func getToolNames(tools []tools.Tool) []string {
	names := make([]string, len(tools))
	for i, tool := range tools {
		names[i] = tool.GetName()
	}
	return names
}

// Background workers

func (a *AdvancedCodingAgent) processRequestQueue() {
	for {
		select {
		case <-a.ctx.Done():
			return
		case req := <-a.requestQueue:
			if req != nil {
				a.processRequest(req)
			}
		}
	}
}

func (a *AdvancedCodingAgent) cleanupCache() {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-a.ctx.Done():
			return
		case <-ticker.C:
			// Cleanup cache (simplified implementation)
			// a.responseCache.cleanup()
		}
	}
}

// generateHelpText generates help text for the /help command
func (a *AdvancedCodingAgent) generateHelpText() string {
	return `🔧 Available Commands:

📋 Basic Commands:
  /help      - Show this help message
  /config    - Open configuration menu
  /status    - Show agent status and statistics
  /sessions  - List and manage conversation sessions
  /clear     - Clear conversation history
  /exit      - Exit the application

🛠️ Tool Commands:
  /tools     - List all available tools
  /providers - Show AI provider status

🎯 Ultra-Powerful Features:
  • 154+ intelligent tools for development workflow
  • Multi-provider AI support (OpenAI, Gemini, Mistral, DeepSeek, Groq, Together AI)
  • Full-stack project generation and autonomous coding

💡 Usage Tips:
  • Just type your question or request naturally
  • Use specific commands like "create a file" or "run tests"
  • Ask for code generation, debugging, or optimization`
}

// generateStatusText generates status text for the /status command
func (a *AdvancedCodingAgent) generateStatusText() string {
	status := a.GetStatus()

	return fmt.Sprintf(`🔍 Agent Status:
  Version: %v
  Status: %v
  Tools: %v
  Providers: %v
  Current Session: %v
  Memory Size: %v
  Cache Size: %v
  Processing: %v
  Uptime: %v`,
		status["version"],
		status["status"],
		status["tools_count"],
		status["providers_count"],
		status["current_session"],
		status["memory_size"],
		status["cache_size"],
		status["is_processing"],
		status["uptime"])
}

// generateToolsList generates tools list for the /tools command
func (a *AdvancedCodingAgent) generateToolsList() string {
	tools := a.toolManager.GetAllToolNames()
	return fmt.Sprintf("🛠️ Available Tools (%d total):\n\nFile System, Web & API, Terminal, Code Analysis, Version Control, Testing, Ultra-Powerful tools and more!", len(tools))
}

// generateProvidersList generates providers list for the /providers command
func (a *AdvancedCodingAgent) generateProvidersList() string {
	providers := a.providerManager.GetAvailableProviders()
	return fmt.Sprintf("🧠 AI Providers (%d total):\n\nOpenAI, Gemini, Mistral, DeepSeek, Groq, Together AI, Claude", len(providers))
}

// getCachedResponse retrieves cached response
func (a *AdvancedCodingAgent) getCachedResponse(message string) *Response {
	return nil // Simple implementation
}

// cacheResponse caches a response
func (a *AdvancedCodingAgent) cacheResponse(message string, response *Response) {
	// Simple implementation
}
