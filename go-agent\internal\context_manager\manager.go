package context_manager

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// ContextItem represents a single context item
type ContextItem struct {
	Key       string                 `json:"key"`
	Value     interface{}            `json:"value"`
	Timestamp time.Time              `json:"timestamp"`
	TTL       time.Duration          `json:"ttl"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// Manager manages conversation context and state
type Manager struct {
	context     map[string]*ContextItem
	maxSize     int
	defaultTTL  time.Duration
	mu          sync.RWMutex
}

// NewManager creates a new context manager
func NewManager() *Manager {
	return &Manager{
		context:    make(map[string]*ContextItem),
		maxSize:    1000,
		defaultTTL: time.Hour * 24, // 24 hours default TTL
	}
}

// Set sets a context value
func (m *Manager) Set(key string, value interface{}) {
	m.SetWithTTL(key, value, m.defaultTTL)
}

// SetWithTTL sets a context value with a specific TTL
func (m *Manager) SetWithTTL(key string, value interface{}, ttl time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	item := &ContextItem{
		Key:       key,
		Value:     value,
		Timestamp: time.Now(),
		TTL:       ttl,
		Metadata:  make(map[string]interface{}),
	}
	
	m.context[key] = item
	
	// Clean up expired items if we're at max size
	if len(m.context) > m.maxSize {
		m.cleanup()
	}
}

// Get retrieves a context value
func (m *Manager) Get(key string) (interface{}, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	item, exists := m.context[key]
	if !exists {
		return nil, false
	}
	
	// Check if expired
	if m.isExpired(item) {
		delete(m.context, key)
		return nil, false
	}
	
	return item.Value, true
}

// GetString retrieves a context value as string
func (m *Manager) GetString(key string) (string, bool) {
	value, exists := m.Get(key)
	if !exists {
		return "", false
	}
	
	if str, ok := value.(string); ok {
		return str, true
	}
	
	return fmt.Sprintf("%v", value), true
}

// GetInt retrieves a context value as int
func (m *Manager) GetInt(key string) (int, bool) {
	value, exists := m.Get(key)
	if !exists {
		return 0, false
	}
	
	if i, ok := value.(int); ok {
		return i, true
	}
	
	if f, ok := value.(float64); ok {
		return int(f), true
	}
	
	return 0, false
}

// Delete removes a context value
func (m *Manager) Delete(key string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	delete(m.context, key)
}

// Clear clears all context
func (m *Manager) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.context = make(map[string]*ContextItem)
}

// GetCurrentContext returns the current context as a map
func (m *Manager) GetCurrentContext() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string]interface{})
	
	for key, item := range m.context {
		if !m.isExpired(item) {
			result[key] = item.Value
		}
	}
	
	return result
}

// UpdateContext updates context based on user input and response
func (m *Manager) UpdateContext(userInput, response string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Update last interaction
	m.context["last_user_input"] = &ContextItem{
		Key:       "last_user_input",
		Value:     userInput,
		Timestamp: time.Now(),
		TTL:       m.defaultTTL,
		Metadata:  make(map[string]interface{}),
	}
	
	m.context["last_response"] = &ContextItem{
		Key:       "last_response",
		Value:     response,
		Timestamp: time.Now(),
		TTL:       m.defaultTTL,
		Metadata:  make(map[string]interface{}),
	}
	
	// Extract and store context from user input
	m.extractContextFromInput(userInput)
	
	// Extract and store context from response
	m.extractContextFromResponse(response)
	
	// Update interaction count
	if countItem, exists := m.context["interaction_count"]; exists {
		if count, ok := countItem.Value.(int); ok {
			m.context["interaction_count"].Value = count + 1
		}
	} else {
		m.context["interaction_count"] = &ContextItem{
			Key:       "interaction_count",
			Value:     1,
			Timestamp: time.Now(),
			TTL:       time.Hour * 24 * 7, // Keep for a week
			Metadata:  make(map[string]interface{}),
		}
	}
}

// GetContextSummary returns a summary of the current context
func (m *Manager) GetContextSummary() string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var summary strings.Builder
	summary.WriteString("Current Context:\n")
	
	// Group context by type
	categories := map[string][]string{
		"Session": {},
		"Files":   {},
		"Code":    {},
		"Other":   {},
	}
	
	for key, item := range m.context {
		if m.isExpired(item) {
			continue
		}
		
		category := m.categorizeContextKey(key)
		value := fmt.Sprintf("%v", item.Value)
		if len(value) > 50 {
			value = value[:47] + "..."
		}
		
		categories[category] = append(categories[category], fmt.Sprintf("  %s: %s", key, value))
	}
	
	for category, items := range categories {
		if len(items) > 0 {
			summary.WriteString(fmt.Sprintf("\n%s:\n", category))
			for _, item := range items {
				summary.WriteString(item + "\n")
			}
		}
	}
	
	return summary.String()
}

// GetSize returns the current context size
func (m *Manager) GetSize() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// Count non-expired items
	count := 0
	for _, item := range m.context {
		if !m.isExpired(item) {
			count++
		}
	}
	
	return count
}

// SetMaxSize sets the maximum context size
func (m *Manager) SetMaxSize(size int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.maxSize = size
	
	if len(m.context) > m.maxSize {
		m.cleanup()
	}
}

// SetDefaultTTL sets the default TTL for context items
func (m *Manager) SetDefaultTTL(ttl time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.defaultTTL = ttl
}

// GetStats returns context statistics
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	expired := 0
	for _, item := range m.context {
		if m.isExpired(item) {
			expired++
		}
	}
	
	return map[string]interface{}{
		"total_items":    len(m.context),
		"active_items":   len(m.context) - expired,
		"expired_items":  expired,
		"max_size":       m.maxSize,
		"default_ttl":    m.defaultTTL.String(),
	}
}

// Private methods

func (m *Manager) isExpired(item *ContextItem) bool {
	return time.Since(item.Timestamp) > item.TTL
}

func (m *Manager) cleanup() {
	// Remove expired items first
	for key, item := range m.context {
		if m.isExpired(item) {
			delete(m.context, key)
		}
	}
	
	// If still over limit, remove oldest items
	if len(m.context) > m.maxSize {
		// Convert to slice for sorting
		items := make([]*ContextItem, 0, len(m.context))
		for _, item := range m.context {
			items = append(items, item)
		}
		
		// Sort by timestamp (oldest first)
		for i := 0; i < len(items)-1; i++ {
			for j := i + 1; j < len(items); j++ {
				if items[i].Timestamp.After(items[j].Timestamp) {
					items[i], items[j] = items[j], items[i]
				}
			}
		}
		
		// Remove oldest items
		toRemove := len(m.context) - m.maxSize
		for i := 0; i < toRemove; i++ {
			delete(m.context, items[i].Key)
		}
	}
}

func (m *Manager) extractContextFromInput(input string) {
	inputLower := strings.ToLower(input)
	
	// Extract file references
	if strings.Contains(inputLower, "file") || strings.Contains(inputLower, ".py") || strings.Contains(inputLower, ".js") {
		m.context["working_with_files"] = &ContextItem{
			Key:       "working_with_files",
			Value:     true,
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	}
	
	// Extract programming language context
	languages := []string{"python", "javascript", "go", "java", "rust", "cpp", "c++"}
	for _, lang := range languages {
		if strings.Contains(inputLower, lang) {
			m.context["current_language"] = &ContextItem{
				Key:       "current_language",
				Value:     lang,
				Timestamp: time.Now(),
				TTL:       time.Hour * 2,
				Metadata:  make(map[string]interface{}),
			}
			break
		}
	}
	
	// Extract task type
	if strings.Contains(inputLower, "debug") || strings.Contains(inputLower, "fix") {
		m.context["current_task"] = &ContextItem{
			Key:       "current_task",
			Value:     "debugging",
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	} else if strings.Contains(inputLower, "create") || strings.Contains(inputLower, "generate") {
		m.context["current_task"] = &ContextItem{
			Key:       "current_task",
			Value:     "creation",
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	}
}

func (m *Manager) extractContextFromResponse(response string) {
	responseLower := strings.ToLower(response)
	
	// Track if code was provided
	if strings.Contains(response, "```") {
		m.context["code_provided"] = &ContextItem{
			Key:       "code_provided",
			Value:     true,
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	}
	
	// Track success/failure
	if strings.Contains(responseLower, "error") || strings.Contains(responseLower, "failed") {
		m.context["last_result"] = &ContextItem{
			Key:       "last_result",
			Value:     "error",
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	} else if strings.Contains(responseLower, "success") || strings.Contains(responseLower, "completed") {
		m.context["last_result"] = &ContextItem{
			Key:       "last_result",
			Value:     "success",
			Timestamp: time.Now(),
			TTL:       time.Hour,
			Metadata:  make(map[string]interface{}),
		}
	}
}

func (m *Manager) categorizeContextKey(key string) string {
	keyLower := strings.ToLower(key)
	
	if strings.Contains(keyLower, "file") || strings.Contains(keyLower, "directory") {
		return "Files"
	} else if strings.Contains(keyLower, "code") || strings.Contains(keyLower, "language") {
		return "Code"
	} else if strings.Contains(keyLower, "session") || strings.Contains(keyLower, "interaction") {
		return "Session"
	}
	
	return "Other"
}
