package session

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/google/uuid"
)

// Session represents a conversation session
type Session struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsActive  bool      `json:"is_active"`
	Messages  []Message `json:"messages"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// Message represents a single message in a session
type Message struct {
	ID        string                 `json:"id"`
	Role      string                 `json:"role"` // user, assistant, system
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Manager manages conversation sessions
type Manager struct {
	sessions    map[string]*Session
	activeID    string
	storageDir  string
	mu          sync.RWMutex
}

// NewManager creates a new session manager
func NewManager() *Manager {
	home, _ := os.UserHomeDir()
	storageDir := filepath.Join(home, ".go-agent", "sessions")
	
	// Create storage directory if it doesn't exist
	os.MkdirAll(storageDir, 0755)
	
	manager := &Manager{
		sessions:   make(map[string]*Session),
		storageDir: storageDir,
	}
	
	// Load existing sessions
	manager.loadSessions()
	
	return manager
}

// CreateSession creates a new conversation session
func (m *Manager) CreateSession(name string) (*Session, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if name == "" {
		name = fmt.Sprintf("Session %s", time.Now().Format("2006-01-02 15:04"))
	}
	
	session := &Session{
		ID:        uuid.New().String(),
		Name:      name,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		IsActive:  true,
		Messages:  make([]Message, 0),
		Metadata:  make(map[string]interface{}),
	}
	
	// Deactivate other sessions
	for _, s := range m.sessions {
		s.IsActive = false
	}
	
	m.sessions[session.ID] = session
	m.activeID = session.ID
	
	// Save to disk
	if err := m.saveSession(session); err != nil {
		return nil, fmt.Errorf("failed to save session: %w", err)
	}
	
	return session, nil
}

// GetSession retrieves a session by ID
func (m *Manager) GetSession(id string) (*Session, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	session, exists := m.sessions[id]
	if !exists {
		return nil, fmt.Errorf("session not found: %s", id)
	}
	
	return session, nil
}

// GetActiveSession returns the currently active session
func (m *Manager) GetActiveSession() (*Session, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.activeID == "" {
		return nil, fmt.Errorf("no active session")
	}
	
	return m.sessions[m.activeID], nil
}

// SetActiveSession sets the active session
func (m *Manager) SetActiveSession(id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	session, exists := m.sessions[id]
	if !exists {
		return fmt.Errorf("session not found: %s", id)
	}
	
	// Deactivate all sessions
	for _, s := range m.sessions {
		s.IsActive = false
	}
	
	// Activate the selected session
	session.IsActive = true
	m.activeID = id
	
	return nil
}

// GetAllSessions returns all sessions
func (m *Manager) GetAllSessions() []*Session {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	sessions := make([]*Session, 0, len(m.sessions))
	for _, session := range m.sessions {
		sessions = append(sessions, session)
	}
	
	return sessions
}

// AddMessage adds a message to the active session
func (m *Manager) AddMessage(role, content string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.activeID == "" {
		return fmt.Errorf("no active session")
	}
	
	session := m.sessions[m.activeID]
	
	message := Message{
		ID:        uuid.New().String(),
		Role:      role,
		Content:   content,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}
	
	session.Messages = append(session.Messages, message)
	session.UpdatedAt = time.Now()
	
	// Save session
	return m.saveSession(session)
}

// GetMessages returns messages from the active session
func (m *Manager) GetMessages(limit int) ([]Message, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.activeID == "" {
		return nil, fmt.Errorf("no active session")
	}
	
	session := m.sessions[m.activeID]
	messages := session.Messages
	
	if limit > 0 && len(messages) > limit {
		// Return the most recent messages
		messages = messages[len(messages)-limit:]
	}
	
	return messages, nil
}

// ClearSession clears all messages from a session
func (m *Manager) ClearSession(id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	session, exists := m.sessions[id]
	if !exists {
		return fmt.Errorf("session not found: %s", id)
	}
	
	session.Messages = make([]Message, 0)
	session.UpdatedAt = time.Now()
	
	return m.saveSession(session)
}

// DeleteSession deletes a session
func (m *Manager) DeleteSession(id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if _, exists := m.sessions[id]; !exists {
		return fmt.Errorf("session not found: %s", id)
	}
	
	// Remove from memory
	delete(m.sessions, id)
	
	// Remove from disk
	sessionFile := filepath.Join(m.storageDir, id+".json")
	if err := os.Remove(sessionFile); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete session file: %w", err)
	}
	
	// If this was the active session, clear active ID
	if m.activeID == id {
		m.activeID = ""
	}
	
	return nil
}

// SaveSession saves a session to disk
func (m *Manager) SaveSession(session *Session) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	return m.saveSession(session)
}

// saveSession saves a session to disk (internal method, assumes lock is held)
func (m *Manager) saveSession(session *Session) error {
	sessionFile := filepath.Join(m.storageDir, session.ID+".json")
	
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}
	
	if err := os.WriteFile(sessionFile, data, 0600); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}
	
	return nil
}

// loadSessions loads all sessions from disk
func (m *Manager) loadSessions() {
	files, err := filepath.Glob(filepath.Join(m.storageDir, "*.json"))
	if err != nil {
		return
	}
	
	for _, file := range files {
		session, err := m.loadSession(file)
		if err != nil {
			continue // Skip invalid sessions
		}
		
		m.sessions[session.ID] = session
		
		if session.IsActive {
			m.activeID = session.ID
		}
	}
	
	// If no active session found, create a default one
	if m.activeID == "" && len(m.sessions) == 0 {
		defaultSession, _ := m.CreateSession("default")
		if defaultSession != nil {
			m.activeID = defaultSession.ID
		}
	}
}

// loadSession loads a single session from file
func (m *Manager) loadSession(filename string) (*Session, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	
	var session Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, err
	}
	
	return &session, nil
}

// GetSessionStats returns statistics about sessions
func (m *Manager) GetSessionStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	totalMessages := 0
	for _, session := range m.sessions {
		totalMessages += len(session.Messages)
	}
	
	return map[string]interface{}{
		"total_sessions":  len(m.sessions),
		"active_session":  m.activeID,
		"total_messages":  totalMessages,
		"storage_dir":     m.storageDir,
	}
}

// ExportSession exports a session to a file
func (m *Manager) ExportSession(id, filename string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	session, exists := m.sessions[id]
	if !exists {
		return fmt.Errorf("session not found: %s", id)
	}
	
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}
	
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}
	
	return nil
}

// ImportSession imports a session from a file
func (m *Manager) ImportSession(filename string) (*Session, error) {
	session, err := m.loadSession(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to load session: %w", err)
	}
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Generate new ID to avoid conflicts
	session.ID = uuid.New().String()
	session.IsActive = false
	session.UpdatedAt = time.Now()
	
	m.sessions[session.ID] = session
	
	// Save to storage
	if err := m.saveSession(session); err != nil {
		return nil, fmt.Errorf("failed to save imported session: %w", err)
	}
	
	return session, nil
}
