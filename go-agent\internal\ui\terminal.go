package ui

import (
	"fmt"
	"strings"
	"time"

	"github.com/fatih/color"
	"go-agent/internal/session"
)

// TerminalUI handles the terminal user interface
type TerminalUI struct {
	theme         Theme
	showTimestamp bool
	showTokens    bool
}

// Theme defines color schemes
type Theme struct {
	Primary   *color.Color
	Secondary *color.Color
	Success   *color.Color
	Warning   *color.Color
	Error     *color.Color
	Info      *color.Color
	Muted     *color.Color
}

// NewTerminalUI creates a new terminal UI instance
func NewTerminalUI() *TerminalUI {
	return &TerminalUI{
		theme:         getDarkTheme(),
		showTimestamp: true,
		showTokens:    true,
	}
}

// ShowWelcome displays the welcome message
func (ui *TerminalUI) ShowWelcome(version string) {
	ui.Clear()
	
	banner := `
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ██████╗ ██████╗ ███████╗███╗   ██╗ ██████╗ ██████╗ ██████╗ ███████╗      ║
║   ██╔═══██╗██╔══██╗██╔════╝████╗  ██║██╔════╝██╔═══██╗██╔══██╗██╔════╝      ║
║   ██║   ██║██████╔╝█████╗  ██╔██╗ ██║██║     ██║   ██║██║  ██║█████╗        ║
║   ██║   ██║██╔═══╝ ██╔══╝  ██║╚██╗██║██║     ██║   ██║██║  ██║██╔══╝        ║
║   ╚██████╔╝██║     ███████╗██║ ╚████║╚██████╗╚██████╔╝██████╔╝███████╗      ║
║    ╚═════╝ ╚═╝     ╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝      ║
║                                                                              ║
║                    AI Terminal Agent - Go Edition                           ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝`

	ui.theme.Primary.Println(banner)
	
	fmt.Printf("\n🚀 %s %s\n", ui.theme.Success.Sprint("OpenCode AI Terminal Agent"), ui.theme.Info.Sprintf("v%s", version))
	fmt.Printf("🧠 %s\n", ui.theme.Secondary.Sprint("Ultra-powerful AI coding assistant with 154+ tools"))
	fmt.Printf("⚡ %s\n", ui.theme.Info.Sprint("Built with Go for maximum performance and reliability"))
	
	fmt.Printf("\n%s\n", ui.theme.Muted.Sprint("Type /help for available commands or start chatting!"))
	fmt.Printf("%s\n\n", ui.theme.Muted.Sprint("Press Ctrl+C to exit"))
}

// ShowPrompt displays the input prompt
func (ui *TerminalUI) ShowPrompt() {
	timestamp := ""
	if ui.showTimestamp {
		timestamp = ui.theme.Muted.Sprintf("[%s] ", time.Now().Format("15:04:05"))
	}
	
	fmt.Printf("%s%s ", timestamp, ui.theme.Primary.Sprint("🤖 >"))
}

// ShowResponse displays the agent's response
func (ui *TerminalUI) ShowResponse(response string) {
	timestamp := ""
	if ui.showTimestamp {
		timestamp = ui.theme.Muted.Sprintf("[%s] ", time.Now().Format("15:04:05"))
	}
	
	fmt.Printf("\n%s%s\n", timestamp, ui.theme.Success.Sprint("🧠 Agent:"))
	
	// Format and display response
	ui.formatResponse(response)
	fmt.Println()
}

// ShowError displays an error message
func (ui *TerminalUI) ShowError(message string) {
	timestamp := ""
	if ui.showTimestamp {
		timestamp = ui.theme.Muted.Sprintf("[%s] ", time.Now().Format("15:04:05"))
	}
	
	fmt.Printf("\n%s%s %s\n\n", timestamp, ui.theme.Error.Sprint("❌ Error:"), message)
}

// ShowHelp displays the help information
func (ui *TerminalUI) ShowHelp() {
	help := `
🔧 Available Commands:

📋 Basic Commands:
  /help      - Show this help message
  /config    - Open configuration menu
  /status    - Show agent status and statistics
  /sessions  - List and manage conversation sessions
  /clear     - Clear conversation history
  /exit      - Exit the application
  /version   - Show version information

🛠️ Tool Commands:
  /tools     - List all available tools
  /providers - Show AI provider status

🎯 Ultra-Powerful Features:
  • 154+ intelligent tools for development workflow
  • Multi-provider AI support (OpenAI, Gemini, Mistral, DeepSeek, Groq, Together AI)
  • Full-stack project generation and autonomous coding
  • Advanced context management and smart caching
  • Large codebase support with intelligent chunking

💡 Usage Tips:
  • Just type your question or request naturally
  • Use specific commands like "create a file" or "run tests"
  • Ask for code generation, debugging, or optimization
  • Request project analysis or architecture advice

🚀 Examples:
  "Create a REST API in Go"
  "Debug this Python code: [paste code]"
  "Optimize my React component for performance"
  "Generate unit tests for this function"
  "Analyze my project structure"
`
	
	ui.theme.Info.Println(help)
}

// ShowStatus displays the agent status
func (ui *TerminalUI) ShowStatus(status map[string]interface{}) {
	fmt.Printf("\n%s\n", ui.theme.Primary.Sprint("🔍 Agent Status"))
	fmt.Println(strings.Repeat("─", 50))
	
	for key, value := range status {
		label := ui.formatStatusLabel(key)
		fmt.Printf("%-20s: %v\n", label, ui.formatStatusValue(value))
	}
	
	fmt.Println()
}

// ShowSessions displays available sessions
func (ui *TerminalUI) ShowSessions(sessions []*session.Session) {
	fmt.Printf("\n%s\n", ui.theme.Primary.Sprint("💬 Conversation Sessions"))
	fmt.Println(strings.Repeat("─", 70))
	
	if len(sessions) == 0 {
		fmt.Printf("%s\n\n", ui.theme.Muted.Sprint("No sessions found"))
		return
	}
	
	fmt.Printf("%-20s %-15s %-20s %s\n", "ID", "Status", "Created", "Messages")
	fmt.Println(strings.Repeat("─", 70))
	
	for _, sess := range sessions {
		status := ui.theme.Success.Sprint("Active")
		if !sess.IsActive {
			status = ui.theme.Muted.Sprint("Inactive")
		}
		
		fmt.Printf("%-20s %-15s %-20s %d\n",
			sess.ID[:20],
			status,
			sess.CreatedAt.Format("2006-01-02 15:04"),
			len(sess.Messages))
	}
	
	fmt.Println()
}

// ShowTools displays available tools
func (ui *TerminalUI) ShowTools(tools []string) {
	fmt.Printf("\n%s\n", ui.theme.Primary.Sprint("🛠️ Available Tools"))
	fmt.Println(strings.Repeat("─", 50))
	
	categories := ui.categorizeTools(tools)
	
	for category, categoryTools := range categories {
		fmt.Printf("\n%s:\n", ui.theme.Secondary.Sprint(category))
		for _, tool := range categoryTools {
			fmt.Printf("  • %s\n", tool)
		}
	}
	
	fmt.Printf("\n%s: %d tools\n\n", ui.theme.Info.Sprint("Total"), len(tools))
}

// ShowProviders displays AI provider information
func (ui *TerminalUI) ShowProviders(providers []string) {
	fmt.Printf("\n%s\n", ui.theme.Primary.Sprint("🧠 AI Providers"))
	fmt.Println(strings.Repeat("─", 50))
	
	for _, provider := range providers {
		status := ui.theme.Success.Sprint("✅ Available")
		fmt.Printf("%-15s: %s\n", provider, status)
	}
	
	fmt.Printf("\n%s: %d providers\n\n", ui.theme.Info.Sprint("Total"), len(providers))
}

// Clear clears the terminal screen
func (ui *TerminalUI) Clear() {
	fmt.Print("\033[2J\033[H")
}

// SetTheme sets the UI theme
func (ui *TerminalUI) SetTheme(themeName string) {
	switch themeName {
	case "dark":
		ui.theme = getDarkTheme()
	case "light":
		ui.theme = getLightTheme()
	default:
		ui.theme = getDarkTheme()
	}
}

// Helper methods

func (ui *TerminalUI) formatResponse(response string) {
	lines := strings.Split(response, "\n")
	
	for _, line := range lines {
		if strings.HasPrefix(line, "```") {
			// Code block
			ui.theme.Info.Println(line)
		} else if strings.HasPrefix(line, "# ") {
			// Header
			ui.theme.Primary.Println(line)
		} else if strings.HasPrefix(line, "## ") {
			// Subheader
			ui.theme.Secondary.Println(line)
		} else if strings.HasPrefix(line, "- ") || strings.HasPrefix(line, "* ") {
			// List item
			fmt.Printf("  %s\n", line)
		} else if strings.Contains(line, "✅") {
			// Success message
			ui.theme.Success.Println(line)
		} else if strings.Contains(line, "❌") {
			// Error message
			ui.theme.Error.Println(line)
		} else if strings.Contains(line, "⚠️") {
			// Warning message
			ui.theme.Warning.Println(line)
		} else if strings.Contains(line, "💡") {
			// Info message
			ui.theme.Info.Println(line)
		} else {
			// Regular text
			fmt.Println(line)
		}
	}
}

func (ui *TerminalUI) formatStatusLabel(key string) string {
	// Convert snake_case to Title Case
	parts := strings.Split(key, "_")
	for i, part := range parts {
		if len(part) > 0 {
			parts[i] = strings.ToUpper(part[:1]) + part[1:]
		}
	}
	return strings.Join(parts, " ")
}

func (ui *TerminalUI) formatStatusValue(value interface{}) string {
	switch v := value.(type) {
	case bool:
		if v {
			return ui.theme.Success.Sprint("✅ Yes")
		}
		return ui.theme.Error.Sprint("❌ No")
	case time.Duration:
		return ui.theme.Info.Sprint(v.String())
	case int, int64:
		return ui.theme.Info.Sprintf("%v", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func (ui *TerminalUI) categorizeTools(tools []string) map[string][]string {
	categories := make(map[string][]string)
	
	for _, tool := range tools {
		category := "Other"
		
		if strings.Contains(tool, "file") || strings.Contains(tool, "directory") {
			category = "File System"
		} else if strings.Contains(tool, "web") || strings.Contains(tool, "http") {
			category = "Web & API"
		} else if strings.Contains(tool, "terminal") || strings.Contains(tool, "command") {
			category = "Terminal"
		} else if strings.Contains(tool, "code") || strings.Contains(tool, "analyze") {
			category = "Code Analysis"
		} else if strings.Contains(tool, "git") {
			category = "Version Control"
		} else if strings.Contains(tool, "test") {
			category = "Testing"
		} else if strings.Contains(tool, "ultra") || strings.Contains(tool, "autonomous") {
			category = "Ultra-Powerful"
		}
		
		categories[category] = append(categories[category], tool)
	}
	
	return categories
}

// Theme definitions

func getDarkTheme() Theme {
	return Theme{
		Primary:   color.New(color.FgCyan, color.Bold),
		Secondary: color.New(color.FgBlue),
		Success:   color.New(color.FgGreen),
		Warning:   color.New(color.FgYellow),
		Error:     color.New(color.FgRed),
		Info:      color.New(color.FgMagenta),
		Muted:     color.New(color.FgHiBlack),
	}
}

func getLightTheme() Theme {
	return Theme{
		Primary:   color.New(color.FgBlue, color.Bold),
		Secondary: color.New(color.FgCyan),
		Success:   color.New(color.FgGreen),
		Warning:   color.New(color.FgYellow),
		Error:     color.New(color.FgRed),
		Info:      color.New(color.FgMagenta),
		Muted:     color.New(color.FgBlack),
	}
}
