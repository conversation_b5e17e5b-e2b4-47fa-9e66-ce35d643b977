package providers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"sync"
	"time"

	"go-agent/internal/config"
)

// MistralProvider implements the Provider interface for Mistral AI
type MistralProvider struct {
	config       *config.Config
	currentModel string
	usage        Usage
	mu           sync.RWMutex
	client       *http.Client
}

// MistralRequest represents a request to Mistral API
type MistralRequest struct {
	Model       string           `json:"model"`
	Messages    []MistralMessage `json:"messages"`
	Temperature float64          `json:"temperature"`
	MaxTokens   int              `json:"max_tokens"`
	Stream      bool             `json:"stream"`
}

// MistralMessage represents a message in Mistral format
type MistralMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// MistralResponse represents Mistral API response
type MistralResponse struct {
	ID      string          `json:"id"`
	Object  string          `json:"object"`
	Created int64           `json:"created"`
	Model   string          `json:"model"`
	Choices []MistralChoice `json:"choices"`
	Usage   MistralUsage    `json:"usage"`
}

// MistralChoice represents a response choice
type MistralChoice struct {
	Index        int            `json:"index"`
	Message      MistralMessage `json:"message"`
	FinishReason string         `json:"finish_reason"`
}

// MistralUsage represents usage statistics
type MistralUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// NewMistralProvider creates a new Mistral provider
func NewMistralProvider(cfg *config.Config) *MistralProvider {
	return &MistralProvider{
		config:       cfg,
		currentModel: "mistral-large-latest",
		usage:        Usage{},
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// GetName returns the provider name
func (p *MistralProvider) GetName() string {
	return "mistral"
}

// IsConfigured checks if the provider is properly configured
func (p *MistralProvider) IsConfigured() bool {
	return p.config.AI.Mistral.APIKey != ""
}

// GenerateResponse generates a response using Mistral
func (p *MistralProvider) GenerateResponse(aiContext AIContext) (string, error) {
	if !p.IsConfigured() {
		return "", fmt.Errorf("Mistral provider not configured")
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	startTime := time.Now()

	// Convert messages to Mistral format
	messages := make([]MistralMessage, len(aiContext.Messages))
	for i, msg := range aiContext.Messages {
		messages[i] = MistralMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Prepare request
	req := MistralRequest{
		Model:       p.currentModel,
		Messages:    messages,
		Temperature: aiContext.Temperature,
		MaxTokens:   aiContext.MaxTokens,
		Stream:      false,
	}

	// Make API call
	response, err := p.makeAPICall(req)
	if err != nil {
		p.usage.ErrorCount++
		return "", fmt.Errorf("Mistral API error: %w", err)
	}

	// Update usage statistics
	p.updateUsage(startTime, response.Usage)

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from Mistral")
	}

	return response.Choices[0].Message.Content, nil
}

// GetModels returns available models
func (p *MistralProvider) GetModels() []string {
	return []string{
		"mistral-large-latest",
		"mistral-large-2402",
		"mistral-medium-latest",
		"mistral-small-latest",
		"mistral-tiny",
		"open-mistral-7b",
		"open-mixtral-8x7b",
		"open-mixtral-8x22b",
	}
}

// SetModel sets the current model
func (p *MistralProvider) SetModel(model string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Validate model
	validModels := p.GetModels()
	for _, validModel := range validModels {
		if model == validModel {
			p.currentModel = model
			return nil
		}
	}

	return fmt.Errorf("invalid model: %s", model)
}

// GetUsage returns usage statistics
func (p *MistralProvider) GetUsage() Usage {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return p.usage
}

// HealthCheck performs a health check
func (p *MistralProvider) HealthCheck() error {
	if !p.IsConfigured() {
		return fmt.Errorf("provider not configured")
	}

	// Simple health check with a minimal request
	req := MistralRequest{
		Model: "mistral-tiny",
		Messages: []MistralMessage{
			{
				Role:    "user",
				Content: "Hello",
			},
		},
		Temperature: 0.1,
		MaxTokens:   5,
		Stream:      false,
	}

	_, err := p.makeAPICall(req)
	return err
}

// makeAPICall makes a request to the Mistral API
func (p *MistralProvider) makeAPICall(req MistralRequest) (*MistralResponse, error) {
	// Prepare URL
	url := "https://api.mistral.ai/v1/chat/completions"

	// Marshal request
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+p.config.AI.Mistral.APIKey)

	// Make request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var mistralResp MistralResponse
	if err := json.Unmarshal(body, &mistralResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &mistralResp, nil
}

// updateUsage updates usage statistics
func (p *MistralProvider) updateUsage(startTime time.Time, usage MistralUsage) {
	duration := time.Since(startTime)

	p.usage.RequestCount++
	p.usage.TokensUsed += int64(usage.TotalTokens)
	p.usage.LastUsed = time.Now()

	// Update average latency
	if p.usage.RequestCount == 1 {
		p.usage.AverageLatency = duration
	} else {
		// Calculate running average
		totalLatency := p.usage.AverageLatency * time.Duration(p.usage.RequestCount-1)
		p.usage.AverageLatency = (totalLatency + duration) / time.Duration(p.usage.RequestCount)
	}
}

// GetCurrentModel returns the current model
func (p *MistralProvider) GetCurrentModel() string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.currentModel
}

// GetTokenLimit returns the token limit for the current model
func (p *MistralProvider) GetTokenLimit() int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	switch p.currentModel {
	case "mistral-large-latest", "mistral-large-2402":
		return 32768
	case "mistral-medium-latest":
		return 32768
	case "mistral-small-latest":
		return 32768
	case "mistral-tiny":
		return 32768
	case "open-mistral-7b":
		return 32768
	case "open-mixtral-8x7b":
		return 32768
	case "open-mixtral-8x22b":
		return 65536
	default:
		return 32768
	}
}

// EstimateTokens estimates the number of tokens in a text
func (p *MistralProvider) EstimateTokens(text string) int {
	// Rough estimation: ~4 characters per token
	return len(text) / 4
}

// CanHandleRequest checks if the request can be handled within token limits
func (p *MistralProvider) CanHandleRequest(aiContext AIContext) bool {
	totalTokens := 0

	// Estimate tokens in messages
	for _, msg := range aiContext.Messages {
		totalTokens += p.EstimateTokens(msg.Content)
	}

	// Add buffer for response
	totalTokens += aiContext.MaxTokens

	return totalTokens <= p.GetTokenLimit()
}

// OptimizeRequest optimizes the request to fit within token limits
func (p *MistralProvider) OptimizeRequest(aiContext AIContext) AIContext {
	if p.CanHandleRequest(aiContext) {
		return aiContext
	}

	// Simple optimization: truncate messages if needed
	optimized := aiContext
	tokenLimit := p.GetTokenLimit() - aiContext.MaxTokens - 1000 // Buffer

	currentTokens := 0
	optimizedMessages := make([]Message, 0)

	// Keep system message if present
	if len(aiContext.Messages) > 0 && aiContext.Messages[0].Role == "system" {
		optimizedMessages = append(optimizedMessages, aiContext.Messages[0])
		currentTokens += p.EstimateTokens(aiContext.Messages[0].Content)
	}

	// Add messages from the end (most recent first)
	for i := len(aiContext.Messages) - 1; i >= 0; i-- {
		msg := aiContext.Messages[i]
		if msg.Role == "system" {
			continue // Already added
		}

		msgTokens := p.EstimateTokens(msg.Content)
		if currentTokens+msgTokens <= tokenLimit {
			optimizedMessages = append([]Message{msg}, optimizedMessages...)
			currentTokens += msgTokens
		} else {
			break
		}
	}

	optimized.Messages = optimizedMessages
	return optimized
}

// GetPricing returns pricing information for the current model
func (p *MistralProvider) GetPricing() map[string]float64 {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// Pricing per 1M tokens (as of 2024)
	pricing := map[string]map[string]float64{
		"mistral-large-latest": {"input": 4.0, "output": 12.0},
		"mistral-large-2402":   {"input": 4.0, "output": 12.0},
		"mistral-medium-latest": {"input": 2.7, "output": 8.1},
		"mistral-small-latest":  {"input": 1.0, "output": 3.0},
		"mistral-tiny":          {"input": 0.25, "output": 0.25},
		"open-mistral-7b":       {"input": 0.25, "output": 0.25},
		"open-mixtral-8x7b":     {"input": 0.7, "output": 0.7},
		"open-mixtral-8x22b":    {"input": 2.0, "output": 6.0},
	}

	if modelPricing, exists := pricing[p.currentModel]; exists {
		return modelPricing
	}

	return map[string]float64{"input": 2.0, "output": 6.0} // Default
}

// EstimateCost estimates the cost of a request
func (p *MistralProvider) EstimateCost(aiContext AIContext) float64 {
	pricing := p.GetPricing()

	inputTokens := 0
	for _, msg := range aiContext.Messages {
		inputTokens += p.EstimateTokens(msg.Content)
	}

	outputTokens := aiContext.MaxTokens

	inputCost := float64(inputTokens) / 1000000.0 * pricing["input"]
	outputCost := float64(outputTokens) / 1000000.0 * pricing["output"]

	return inputCost + outputCost
}
