package tools

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// RunCommandTool executes shell commands
type RunCommandTool struct{}

func (t *RunCommandTool) GetName() string        { return "run_command" }
func (t *RunCommandTool) GetDescription() string { return "Execute shell commands in terminal" }
func (t *RunCommandTool) GetCategory() string    { return "terminal" }

func (t *RunCommandTool) IsRelevant(query string) bool {
	keywords := []string{"run", "execute", "command", "terminal", "shell", "bash"}
	return containsAny(query, keywords)
}

func (t *RunCommandTool) Execute(args map[string]interface{}) (interface{}, error) {
	command, ok := args["command"].(string)
	if !ok {
		return nil, fmt.Errorf("command parameter is required")
	}

	// Get working directory (optional)
	workDir, _ := args["working_dir"].(string)
	if workDir == "" {
		workDir = "."
	}

	// Get timeout (optional, default 30 seconds)
	timeout := 30 * time.Second
	if timeoutSecs, ok := args["timeout"].(float64); ok {
		timeout = time.Duration(timeoutSecs) * time.Second
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// Prepare command based on OS
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.CommandContext(ctx, "cmd", "/C", command)
	} else {
		cmd = exec.CommandContext(ctx, "bash", "-c", command)
	}

	cmd.Dir = workDir

	// Execute command
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Sprintf("❌ Command failed: %s\nOutput: %s", err.Error(), string(output)), nil
	}

	return fmt.Sprintf("✅ Command executed successfully:\n%s", string(output)), nil
}

// GetTerminalOutputTool captures terminal output
type GetTerminalOutputTool struct{}

func (t *GetTerminalOutputTool) GetName() string        { return "get_terminal_output" }
func (t *GetTerminalOutputTool) GetDescription() string { return "Capture and analyze terminal output" }
func (t *GetTerminalOutputTool) GetCategory() string    { return "terminal" }

func (t *GetTerminalOutputTool) IsRelevant(query string) bool {
	keywords := []string{"terminal", "output", "capture", "log"}
	return containsAny(query, keywords)
}

func (t *GetTerminalOutputTool) Execute(args map[string]interface{}) (interface{}, error) {
	_, ok := args["command"].(string)
	if !ok {
		return nil, fmt.Errorf("command parameter is required")
	}

	// Execute command and capture output
	runTool := &RunCommandTool{}
	result, err := runTool.Execute(args)
	if err != nil {
		return nil, err
	}

	// Analyze output for common patterns
	output := result.(string)
	analysis := analyzeTerminalOutput(output)

	return map[string]interface{}{
		"output":   output,
		"analysis": analysis,
	}, nil
}

// InstallPackagesTool installs packages dynamically
type InstallPackagesTool struct{}

func (t *InstallPackagesTool) GetName() string        { return "install_packages" }
func (t *InstallPackagesTool) GetDescription() string { return "Install packages dynamically (pip, npm, go get, etc.)" }
func (t *InstallPackagesTool) GetCategory() string    { return "terminal" }

func (t *InstallPackagesTool) IsRelevant(query string) bool {
	keywords := []string{"install", "package", "pip", "npm", "go get", "dependency"}
	return containsAny(query, keywords)
}

func (t *InstallPackagesTool) Execute(args map[string]interface{}) (interface{}, error) {
	packageName, ok := args["package"].(string)
	if !ok {
		return nil, fmt.Errorf("package parameter is required")
	}

	packageManager, _ := args["manager"].(string)
	if packageManager == "" {
		// Auto-detect package manager
		packageManager = detectPackageManager()
	}

	var installCommand string
	switch packageManager {
	case "pip":
		installCommand = fmt.Sprintf("pip install %s", packageName)
	case "npm":
		installCommand = fmt.Sprintf("npm install %s", packageName)
	case "yarn":
		installCommand = fmt.Sprintf("yarn add %s", packageName)
	case "go":
		installCommand = fmt.Sprintf("go get %s", packageName)
	case "cargo":
		installCommand = fmt.Sprintf("cargo add %s", packageName)
	case "composer":
		installCommand = fmt.Sprintf("composer require %s", packageName)
	default:
		return nil, fmt.Errorf("unsupported package manager: %s", packageManager)
	}

	// Execute installation command
	runTool := &RunCommandTool{}
	result, err := runTool.Execute(map[string]interface{}{
		"command": installCommand,
		"timeout": 300, // 5 minutes for package installation
	})

	if err != nil {
		return nil, err
	}

	return fmt.Sprintf("✅ Package installed successfully: %s\n%s", packageName, result), nil
}

// ConfigureEnvironmentTool sets up development environments
type ConfigureEnvironmentTool struct{}

func (t *ConfigureEnvironmentTool) GetName() string        { return "configure_environment" }
func (t *ConfigureEnvironmentTool) GetDescription() string { return "Setup and manage development environments" }
func (t *ConfigureEnvironmentTool) GetCategory() string    { return "terminal" }

func (t *ConfigureEnvironmentTool) IsRelevant(query string) bool {
	keywords := []string{"environment", "setup", "configure", "venv", "virtualenv", "conda"}
	return containsAny(query, keywords)
}

func (t *ConfigureEnvironmentTool) Execute(args map[string]interface{}) (interface{}, error) {
	envType, ok := args["type"].(string)
	if !ok {
		return nil, fmt.Errorf("type parameter is required (python, node, go, etc.)")
	}

	envName, _ := args["name"].(string)
	if envName == "" {
		envName = "default"
	}

	var commands []string
	var result strings.Builder

	switch envType {
	case "python":
		commands = []string{
			"python -m venv venv",
			getActivateCommand(),
			"pip install --upgrade pip",
		}
		result.WriteString("🐍 Python virtual environment setup:\n")

	case "node":
		commands = []string{
			"npm init -y",
			"npm install",
		}
		result.WriteString("📦 Node.js environment setup:\n")

	case "go":
		commands = []string{
			"go mod init " + envName,
			"go mod tidy",
		}
		result.WriteString("🐹 Go module setup:\n")

	default:
		return nil, fmt.Errorf("unsupported environment type: %s", envType)
	}

	// Execute setup commands
	runTool := &RunCommandTool{}
	for _, cmd := range commands {
		_, err := runTool.Execute(map[string]interface{}{
			"command": cmd,
		})
		if err != nil {
			result.WriteString(fmt.Sprintf("❌ Failed to execute: %s\n", cmd))
			result.WriteString(fmt.Sprintf("Error: %v\n", err))
		} else {
			result.WriteString(fmt.Sprintf("✅ %s\n", cmd))
		}
	}

	return result.String(), nil
}

// RunTestsTool executes test suites
type RunTestsTool struct{}

func (t *RunTestsTool) GetName() string        { return "run_tests" }
func (t *RunTestsTool) GetDescription() string { return "Run test suites with auto-detection" }
func (t *RunTestsTool) GetCategory() string    { return "terminal" }

func (t *RunTestsTool) IsRelevant(query string) bool {
	keywords := []string{"test", "testing", "pytest", "jest", "go test", "unittest"}
	return containsAny(query, keywords)
}

func (t *RunTestsTool) Execute(args map[string]interface{}) (interface{}, error) {
	testPath, _ := args["path"].(string)
	if testPath == "" {
		testPath = "."
	}

	testType, _ := args["type"].(string)
	if testType == "" {
		testType = detectTestFramework(testPath)
	}

	var testCommand string
	switch testType {
	case "pytest":
		testCommand = "pytest " + testPath + " -v"
	case "unittest":
		testCommand = "python -m unittest discover " + testPath
	case "jest":
		testCommand = "npm test"
	case "go":
		testCommand = "go test ./..."
	case "cargo":
		testCommand = "cargo test"
	case "phpunit":
		testCommand = "phpunit " + testPath
	default:
		return nil, fmt.Errorf("could not detect test framework for path: %s", testPath)
	}

	// Execute tests
	runTool := &RunCommandTool{}
	result, err := runTool.Execute(map[string]interface{}{
		"command": testCommand,
		"timeout": 300, // 5 minutes for tests
	})

	if err != nil {
		return nil, err
	}

	// Parse test results
	testOutput := result.(string)
	testResults := parseTestResults(testOutput, testType)

	return map[string]interface{}{
		"output":  testOutput,
		"results": testResults,
	}, nil
}

// LintCheckTool runs code linting and static analysis
type LintCheckTool struct{}

func (t *LintCheckTool) GetName() string        { return "lint_check" }
func (t *LintCheckTool) GetDescription() string { return "Run code linting and static analysis" }
func (t *LintCheckTool) GetCategory() string    { return "terminal" }

func (t *LintCheckTool) IsRelevant(query string) bool {
	keywords := []string{"lint", "linting", "static", "analysis", "flake8", "eslint", "golint"}
	return containsAny(query, keywords)
}

func (t *LintCheckTool) Execute(args map[string]interface{}) (interface{}, error) {
	path, _ := args["path"].(string)
	if path == "" {
		path = "."
	}

	language, _ := args["language"].(string)
	if language == "" {
		language = detectLanguage(path)
	}

	var commands []string
	switch language {
	case "python":
		commands = []string{
			"flake8 " + path,
			"pylint " + path,
			"mypy " + path,
		}
	case "javascript":
		commands = []string{
			"eslint " + path,
			"jshint " + path,
		}
	case "go":
		commands = []string{
			"go vet ./...",
			"golint ./...",
			"gofmt -l .",
		}
	case "rust":
		commands = []string{
			"cargo clippy",
			"cargo fmt --check",
		}
	default:
		return fmt.Sprintf("⚠️ No linter configured for language: %s", language), nil
	}

	var results strings.Builder
	results.WriteString(fmt.Sprintf("🔍 Linting %s code in %s:\n\n", language, path))

	runTool := &RunCommandTool{}
	for _, cmd := range commands {
		output, err := runTool.Execute(map[string]interface{}{
			"command": cmd,
		})

		results.WriteString(fmt.Sprintf("Command: %s\n", cmd))
		if err != nil {
			results.WriteString(fmt.Sprintf("❌ Error: %v\n", err))
		} else {
			results.WriteString(fmt.Sprintf("✅ Output:\n%s\n", output))
		}
		results.WriteString("\n" + strings.Repeat("-", 50) + "\n\n")
	}

	return results.String(), nil
}

// Helper functions

func detectPackageManager() string {
	// Check for package manager files
	if fileExists("requirements.txt") || fileExists("setup.py") {
		return "pip"
	}
	if fileExists("package.json") {
		if fileExists("yarn.lock") {
			return "yarn"
		}
		return "npm"
	}
	if fileExists("go.mod") {
		return "go"
	}
	if fileExists("Cargo.toml") {
		return "cargo"
	}
	if fileExists("composer.json") {
		return "composer"
	}
	return "unknown"
}

func detectTestFramework(path string) string {
	// Check for test framework indicators
	if fileExists("pytest.ini") || fileExists("setup.cfg") {
		return "pytest"
	}
	if fileExists("package.json") {
		return "jest"
	}
	if fileExists("go.mod") {
		return "go"
	}
	if fileExists("Cargo.toml") {
		return "cargo"
	}
	return "unknown"
}

func detectLanguage(path string) string {
	// Simple language detection based on file extensions
	if hasFilesWithExt(path, ".py") {
		return "python"
	}
	if hasFilesWithExt(path, ".js") || hasFilesWithExt(path, ".ts") {
		return "javascript"
	}
	if hasFilesWithExt(path, ".go") {
		return "go"
	}
	if hasFilesWithExt(path, ".rs") {
		return "rust"
	}
	return "unknown"
}

func getActivateCommand() string {
	if runtime.GOOS == "windows" {
		return "venv\\Scripts\\activate.bat"
	}
	return "source venv/bin/activate"
}

func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

func hasFilesWithExt(path, ext string) bool {
	// Simple check for files with specific extension
	// In a real implementation, you'd walk the directory
	return true // Simplified for this example
}

func analyzeTerminalOutput(output string) map[string]interface{} {
	analysis := make(map[string]interface{})
	
	// Count lines
	lines := strings.Split(output, "\n")
	analysis["line_count"] = len(lines)
	
	// Check for errors
	hasErrors := strings.Contains(strings.ToLower(output), "error") ||
		strings.Contains(strings.ToLower(output), "failed") ||
		strings.Contains(strings.ToLower(output), "exception")
	analysis["has_errors"] = hasErrors
	
	// Check for warnings
	hasWarnings := strings.Contains(strings.ToLower(output), "warning") ||
		strings.Contains(strings.ToLower(output), "warn")
	analysis["has_warnings"] = hasWarnings
	
	return analysis
}

func parseTestResults(output, testType string) map[string]interface{} {
	results := make(map[string]interface{})
	
	// Simple parsing - in a real implementation, you'd parse specific formats
	results["framework"] = testType
	results["output_length"] = len(output)
	
	// Look for common test result patterns
	if strings.Contains(output, "PASSED") || strings.Contains(output, "OK") {
		results["status"] = "passed"
	} else if strings.Contains(output, "FAILED") || strings.Contains(output, "ERROR") {
		results["status"] = "failed"
	} else {
		results["status"] = "unknown"
	}
	
	return results
}
