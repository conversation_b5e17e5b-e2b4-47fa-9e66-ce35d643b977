package providers

import (
	"fmt"
	"sync"
	"time"

	"go-agent/internal/config"
)

// Provider interface defines the contract for AI providers
type Provider interface {
	GetName() string
	IsConfigured() bool
	GenerateResponse(context AIContext) (string, error)
	GetModels() []string
	SetModel(model string) error
	GetUsage() Usage
	HealthCheck() error
}

// AIContext contains the context for AI generation
type AIContext struct {
	Messages    []Message              `json:"messages"`
	Tools       []ToolDescription      `json:"tools"`
	MaxTokens   int                    `json:"max_tokens"`
	Temperature float64                `json:"temperature"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// Message represents a conversation message
type Message struct {
	Role    string `json:"role"`    // system, user, assistant, tool
	Content string `json:"content"`
	Name    string `json:"name,omitempty"`
}

// ToolDescription describes available tools to the AI
type ToolDescription struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// Usage tracks API usage statistics
type Usage struct {
	RequestCount  int64     `json:"request_count"`
	TokensUsed    int64     `json:"tokens_used"`
	LastUsed      time.Time `json:"last_used"`
	ErrorCount    int64     `json:"error_count"`
	AverageLatency time.Duration `json:"average_latency"`
}

// Manager manages all AI providers
type Manager struct {
	providers     map[string]Provider
	activeProvider string
	config        *config.Config
	mu            sync.RWMutex
	healthStatus  map[string]bool
}

// NewManager creates a new provider manager
func NewManager() *Manager {
	manager := &Manager{
		providers:    make(map[string]Provider),
		healthStatus: make(map[string]bool),
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		// Use default config if loading fails
		cfg = config.Default()
	}
	manager.config = cfg

	// Register all providers
	manager.registerProviders()

	// Set active provider
	manager.setActiveProvider()

	// Start health monitoring
	go manager.monitorHealth()

	return manager
}

// registerProviders registers all available AI providers
func (m *Manager) registerProviders() {
	// OpenAI Provider
	openaiProvider := NewOpenAIProvider(m.config)
	m.providers["openai"] = openaiProvider

	// Gemini Provider
	geminiProvider := NewGeminiProvider(m.config)
	m.providers["gemini"] = geminiProvider

	// Mistral Provider
	mistralProvider := NewMistralProvider(m.config)
	m.providers["mistral"] = mistralProvider

	// DeepSeek Provider
	deepseekProvider := NewDeepSeekProvider(m.config)
	m.providers["deepseek"] = deepseekProvider

	// Groq Provider
	groqProvider := NewGroqProvider(m.config)
	m.providers["groq"] = groqProvider

	// Together AI Provider
	togetherProvider := NewTogetherProvider(m.config)
	m.providers["together"] = togetherProvider

	// Anthropic Claude Provider
	claudeProvider := NewClaudeProvider(m.config)
	m.providers["claude"] = claudeProvider
}

// setActiveProvider sets the active provider based on configuration
func (m *Manager) setActiveProvider() {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Try to use configured provider
	if m.config.AI.DefaultProvider != "" {
		if provider, exists := m.providers[m.config.AI.DefaultProvider]; exists && provider.IsConfigured() {
			m.activeProvider = m.config.AI.DefaultProvider
			return
		}
	}

	// Fallback to first configured provider
	for name, provider := range m.providers {
		if provider.IsConfigured() {
			m.activeProvider = name
			return
		}
	}

	// No configured providers
	m.activeProvider = ""
}

// GetActiveProvider returns the currently active provider
func (m *Manager) GetActiveProvider() (Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.activeProvider == "" {
		return nil, fmt.Errorf("no AI provider configured")
	}

	provider, exists := m.providers[m.activeProvider]
	if !exists {
		return nil, fmt.Errorf("active provider '%s' not found", m.activeProvider)
	}

	return provider, nil
}

// SetActiveProvider sets the active provider
func (m *Manager) SetActiveProvider(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	provider, exists := m.providers[name]
	if !exists {
		return fmt.Errorf("provider '%s' not found", name)
	}

	if !provider.IsConfigured() {
		return fmt.Errorf("provider '%s' is not configured", name)
	}

	m.activeProvider = name
	return nil
}

// GetProvider returns a specific provider by name
func (m *Manager) GetProvider(name string) (Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	provider, exists := m.providers[name]
	if !exists {
		return nil, fmt.Errorf("provider '%s' not found", name)
	}

	return provider, nil
}

// GetAvailableProviders returns names of all available providers
func (m *Manager) GetAvailableProviders() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	providers := make([]string, 0, len(m.providers))
	for name := range m.providers {
		providers = append(providers, name)
	}

	return providers
}

// GetConfiguredProviders returns names of configured providers
func (m *Manager) GetConfiguredProviders() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	providers := make([]string, 0)
	for name, provider := range m.providers {
		if provider.IsConfigured() {
			providers = append(providers, name)
		}
	}

	return providers
}

// GetProviderCount returns the total number of providers
func (m *Manager) GetProviderCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return len(m.providers)
}

// GetProviderStatus returns status information for all providers
func (m *Manager) GetProviderStatus() map[string]ProviderStatus {
	m.mu.RLock()
	defer m.mu.RUnlock()

	status := make(map[string]ProviderStatus)
	for name, provider := range m.providers {
		status[name] = ProviderStatus{
			Name:        name,
			Configured:  provider.IsConfigured(),
			Active:      name == m.activeProvider,
			Healthy:     m.healthStatus[name],
			Usage:       provider.GetUsage(),
			Models:      provider.GetModels(),
		}
	}

	return status
}

// GenerateResponse generates a response using the active provider with fallback
func (m *Manager) GenerateResponse(context AIContext) (string, error) {
	// Try active provider first
	provider, err := m.GetActiveProvider()
	if err == nil {
		response, err := provider.GenerateResponse(context)
		if err == nil {
			return response, nil
		}
		// Log error and try fallback
		fmt.Printf("Active provider failed: %v, trying fallback\n", err)
	}

	// Try fallback providers
	configuredProviders := m.GetConfiguredProviders()
	for _, name := range configuredProviders {
		if name == m.activeProvider {
			continue // Skip active provider (already tried)
		}

		provider, err := m.GetProvider(name)
		if err != nil {
			continue
		}

		response, err := provider.GenerateResponse(context)
		if err == nil {
			// Temporarily switch to this provider
			m.SetActiveProvider(name)
			return response, nil
		}
	}

	return "", fmt.Errorf("all configured providers failed")
}

// monitorHealth monitors the health of all providers
func (m *Manager) monitorHealth() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkAllProviderHealth()
		}
	}
}

// checkAllProviderHealth checks the health of all providers
func (m *Manager) checkAllProviderHealth() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for name, provider := range m.providers {
		if !provider.IsConfigured() {
			m.healthStatus[name] = false
			continue
		}

		err := provider.HealthCheck()
		m.healthStatus[name] = err == nil
	}
}

// ProviderStatus represents the status of a provider
type ProviderStatus struct {
	Name       string        `json:"name"`
	Configured bool          `json:"configured"`
	Active     bool          `json:"active"`
	Healthy    bool          `json:"healthy"`
	Usage      Usage         `json:"usage"`
	Models     []string      `json:"models"`
}

// GetBestProvider returns the best available provider based on health and performance
func (m *Manager) GetBestProvider() (Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var bestProvider Provider
	var bestScore float64

	for name, provider := range m.providers {
		if !provider.IsConfigured() {
			continue
		}

		score := m.calculateProviderScore(name, provider)
		if score > bestScore {
			bestScore = score
			bestProvider = provider
		}
	}

	if bestProvider == nil {
		return nil, fmt.Errorf("no suitable provider found")
	}

	return bestProvider, nil
}

// calculateProviderScore calculates a score for provider selection
func (m *Manager) calculateProviderScore(name string, provider Provider) float64 {
	score := 0.0

	// Health check
	if m.healthStatus[name] {
		score += 50.0
	}

	// Usage statistics
	usage := provider.GetUsage()
	if usage.ErrorCount == 0 {
		score += 30.0
	} else {
		// Penalize high error rates
		errorRate := float64(usage.ErrorCount) / float64(usage.RequestCount)
		score += 30.0 * (1.0 - errorRate)
	}

	// Latency (lower is better)
	if usage.AverageLatency > 0 {
		latencyScore := 20.0 / (1.0 + float64(usage.AverageLatency.Milliseconds())/1000.0)
		score += latencyScore
	}

	return score
}

// Shutdown gracefully shuts down the provider manager
func (m *Manager) Shutdown() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Perform any cleanup needed
	return nil
}
