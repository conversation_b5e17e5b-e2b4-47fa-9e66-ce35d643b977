```python
import logging

# Configure logging for better debugging and monitoring
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def greet_user(user_greeting: str) -> str:
    """
    Responds to the user's greeting with a friendly message.

    Args:
        user_greeting: The greeting message from the user (string).

    Returns:
        A friendly response message (string).  Returns an error message if the input is invalid.

    Raises:
        TypeError: If the input is not a string.
        ValueError: If the input string is empty or contains only whitespace.
    """

    try:
        # Input validation: Check if the input is a string
        if not isinstance(user_greeting, str):
            raise TypeError("Input must be a string.")

        # Input validation: Check if the input string is empty or contains only whitespace
        if not user_greeting.strip():
            raise ValueError("Input string cannot be empty or contain only whitespace.")

        # Normalize the greeting to lowercase for easier comparison
        user_greeting = user_greeting.lower()

        # Respond based on the user's greeting
        if "hello" in user_greeting or "hi" in user_greeting or "hey" in user_greeting:
            response = "Hello there! How can I help you today?"
        elif "good morning" in user_greeting:
            response = "Good morning! I hope you have a wonderful day."
        elif "good afternoon" in user_greeting:
            response = "Good afternoon! What can I do for you?"
        elif "good evening" in user_greeting:
            response = "Good evening! How may I assist you?"
        else:
            response = "Greetings! It's a pleasure to interact with you."

        logging.info(f"User said: {user_greeting}. Responded with: {response}")  # Log the interaction
        return response

    except TypeError as e:
        logging.error(f"TypeError: {e}")
        return "Error: Invalid input type. Please provide a string."
    except ValueError as e:
        logging.error(f"ValueError: {e}")
        return "Error: Input string cannot be empty."
    except Exception as e:
        logging.exception("An unexpected error occurred:")  # Log the full exception traceback
        return "Error: An unexpected error occurred. Please try again later."


if __name__ == '__main__':
    # Example usage
    user_input = input("Say hello: ")
    response = greet_user(user_input)
    print(response)

    # Example with error handling
    print(greet_user(123))  # Example of TypeError
    print(greet_user("   "))  # Example of ValueError
```