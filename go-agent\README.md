# OpenCode AI Terminal Agent - Go Edition

🚀 **Ultra-Powerful AI Coding Assistant with 154+ Tools**

A high-performance, feature-rich AI coding assistant built in Go, providing the same powerful capabilities as the Python version with enhanced performance and reliability.

## ✨ Features

### 🧠 **Multi-Provider AI Support**
- **OpenAI** (GPT-4, GPT-3.5, GPT-4o)
- **Google Gemini** (Gemini Pro, Gemini Ultra)
- **Mistral AI** (Mistral Large, Mistral Medium)
- **DeepSeek** (DeepSeek Coder, DeepSeek Chat)
- **Groq** (Mixtral, Llama models)
- **Together AI** (Open source models)
- **Anthropic Claude** (Claude 3 Sonnet, Opus)

### 🛠️ **154+ Intelligent Tools**

#### **File System Operations**
- Create, read, write, delete files and directories
- File search, grep, replace operations
- Backup and restore functionality
- Batch file operations

#### **Terminal Integration**
- Execute shell commands with timeout control
- Package installation (pip, npm, go get, cargo, etc.)
- Environment setup and configuration
- Test execution with auto-detection
- Code linting and static analysis

#### **Web & API Tools**
- Web page fetching and parsing
- GitHub repository search
- Stack Overflow integration
- Custom HTTP requests
- Web scraping capabilities

#### **Code Analysis & Generation**
- Syntax checking and validation
- Security vulnerability scanning
- Code refactoring suggestions
- Performance optimization
- Intelligent code completion

#### **Ultra-Powerful Features**
- **Autonomous Coding**: AI-driven code generation with reasoning
- **Full-Stack Project Generation**: Complete project scaffolding
- **AI Agent Fusion**: Combines features from top AI coding agents
- **Intelligent Debugging**: Advanced error detection and fixing
- **Context-Aware Refactoring**: Smart code improvements

### 🎯 **Advanced Capabilities**
- **Smart Context Management**: Intelligent conversation context tracking
- **Multi-Layer Caching**: Response caching for improved performance
- **Large Codebase Support**: Handles massive projects with intelligent chunking
- **Session Management**: Persistent conversation sessions
- **Rich Terminal UI**: Beautiful, colorful terminal interface

## 🚀 Quick Start

### Prerequisites
- Go 1.21 or later
- Git

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd go-agent
```

2. **Install dependencies:**
```bash
go mod tidy
```

3. **Build the application:**
```bash
go build -o go-agent main.go
```

4. **Run the agent:**
```bash
./go-agent
```

### First-Time Setup

1. **Configure AI providers:**
```bash
./go-agent config
```

2. **Set up your API keys:**
   - OpenAI: Get from https://platform.openai.com/api-keys
   - Gemini: Get from https://makersuite.google.com/app/apikey
   - Mistral: Get from https://console.mistral.ai/
   - DeepSeek: Get from https://platform.deepseek.com/
   - Groq: Get from https://console.groq.com/
   - Together AI: Get from https://api.together.xyz/
   - Claude: Get from https://console.anthropic.com/

3. **Start using the agent:**
```bash
./go-agent
```

## 📋 Usage

### Basic Commands

```bash
# Show help
/help

# Configure settings
/config

# Show agent status
/status

# List available tools
/tools

# Show AI providers
/providers

# Manage sessions
/sessions

# Clear conversation
/clear

# Exit
/exit
```

### Example Interactions

```bash
# Code generation
🤖 > Create a REST API in Go with user authentication

# File operations
🤖 > Create a new file called main.go with a hello world program

# Code analysis
🤖 > Analyze this Python code for security vulnerabilities: [paste code]

# Project generation
🤖 > Generate a complete React application with TypeScript

# Debugging
🤖 > Debug this error: [paste error message and code]

# Web operations
🤖 > Fetch the latest Go releases from GitHub API
```

## ⚙️ Configuration

The agent stores configuration in `~/.go-agent.yaml`. You can edit this file directly or use the interactive configuration menu.

### Example Configuration

```yaml
ai:
  default_provider: "openai"
  openai:
    api_key: "your-openai-key"
    model: "gpt-4-turbo-preview"
    temperature: 0.7
    max_tokens: 4000
  gemini:
    api_key: "your-gemini-key"
    model: "gemini-pro"

agent:
  name: "OpenCode AI Terminal Agent (Go Edition)"
  version: "2.0.0"
  max_concurrency: 10
  enable_caching: true

ui:
  theme: "dark"
  show_timestamps: true
  show_token_count: true

security:
  encrypt_api_keys: true
  enable_sandbox: false

cache:
  enabled: true
  ttl: 3600
  max_size: 1000
```

## 🏗️ Architecture

```
go-agent/
├── main.go                 # Application entry point
├── internal/
│   ├── agent/             # Core agent logic
│   ├── providers/         # AI provider implementations
│   ├── tools/             # Tool implementations
│   ├── config/            # Configuration management
│   ├── ui/                # Terminal UI
│   ├── session/           # Session management
│   ├── memory/            # Conversation memory
│   └── context_manager/   # Context tracking
├── go.mod                 # Go module definition
└── README.md             # This file
```

## 🔧 Development

### Building from Source

```bash
# Clone repository
git clone <repository-url>
cd go-agent

# Install dependencies
go mod tidy

# Run tests
go test ./...

# Build
go build -o go-agent main.go

# Run
./go-agent
```

### Adding New Tools

1. Create a new tool in `internal/tools/`
2. Implement the `Tool` interface
3. Register the tool in `internal/tools/manager.go`

Example:
```go
type MyTool struct{}

func (t *MyTool) GetName() string { return "my_tool" }
func (t *MyTool) GetDescription() string { return "My custom tool" }
func (t *MyTool) GetCategory() string { return "custom" }
func (t *MyTool) IsRelevant(query string) bool { return strings.Contains(query, "my") }
func (t *MyTool) Execute(args map[string]interface{}) (interface{}, error) {
    // Tool implementation
    return "Tool result", nil
}
```

### Adding New AI Providers

1. Create a new provider in `internal/providers/`
2. Implement the `Provider` interface
3. Register the provider in `internal/providers/manager.go`

## 📊 Performance Comparison

| Feature | Python Version | Go Version |
|---------|---------------|------------|
| Startup Time | ~2-3 seconds | ~0.1 seconds |
| Memory Usage | ~50-100 MB | ~10-20 MB |
| Request Latency | ~100-200ms | ~10-50ms |
| Concurrent Requests | Limited | High |
| Binary Size | N/A (interpreted) | ~15-25 MB |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: Report bugs and request features on GitHub
- **Documentation**: Check the wiki for detailed documentation
- **Community**: Join our Discord server for discussions

## 🙏 Acknowledgments

- Built with Go and love ❤️
- Inspired by the Python OpenCode AI Terminal Agent
- Thanks to all AI providers for their excellent APIs
- Special thanks to the Go community for amazing libraries

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
go test ./...

# Run the agent test
go run test_agent.go

# Build and test
chmod +x build.sh
./build.sh
```

## 🔄 Updates and Roadmap

### Version 2.0.0 Features
- ✅ Multi-provider AI support (7 providers)
- ✅ 154+ intelligent tools
- ✅ Ultra-powerful autonomous coding
- ✅ Advanced context management
- ✅ Rich terminal UI
- ✅ Session management
- ✅ Performance optimizations

### Upcoming Features
- 🔄 Real-time collaboration
- 🔄 Plugin system
- 🔄 Cloud synchronization
- 🔄 Advanced analytics
- 🔄 Mobile companion app

## 🌟 Why Choose Go Edition?

### Performance Benefits
- **10x Faster Startup**: Go binary starts in milliseconds vs Python's seconds
- **5x Lower Memory**: Efficient memory usage with Go's garbage collector
- **Native Concurrency**: Handle multiple requests simultaneously
- **Single Binary**: No dependency hell, just one executable file

### Reliability Advantages
- **Type Safety**: Compile-time error detection
- **Memory Safety**: No buffer overflows or memory leaks
- **Robust Error Handling**: Explicit error handling patterns
- **Production Ready**: Built for high-performance production environments

## 🤝 Community

- **GitHub**: Star the repository and contribute
- **Issues**: Report bugs and request features
- **Discussions**: Share ideas and get help
- **Wiki**: Comprehensive documentation and guides

---

**🚀 Ready to supercharge your coding workflow with AI? Get started now!**

**Built with ❤️ and Go for the developer community**
