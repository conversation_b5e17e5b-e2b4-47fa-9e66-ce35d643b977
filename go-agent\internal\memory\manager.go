package memory

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// Interaction represents a single interaction in memory
type Interaction struct {
	ID        string                 `json:"id"`
	Input     string                 `json:"input"`
	Output    string                 `json:"output"`
	Timestamp time.Time              `json:"timestamp"`
	Context   map[string]interface{} `json:"context"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// Manager manages conversation memory and context
type Manager struct {
	interactions []Interaction
	maxSize      int
	storageFile  string
	mu           sync.RWMutex
}

// NewManager creates a new memory manager
func NewManager() *Manager {
	home, _ := os.UserHomeDir()
	storageDir := filepath.Join(home, ".go-agent")
	os.MkdirAll(storageDir, 0755)
	
	manager := &Manager{
		interactions: make([]Interaction, 0),
		maxSize:      1000, // Maximum number of interactions to keep
		storageFile:  filepath.Join(storageDir, "memory.json"),
	}
	
	// Load existing memory
	manager.load()
	
	return manager
}

// AddInteraction adds a new interaction to memory
func (m *Manager) AddInteraction(input, output string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	interaction := Interaction{
		ID:        fmt.Sprintf("%d", time.Now().UnixNano()),
		Input:     input,
		Output:    output,
		Timestamp: time.Now(),
		Context:   make(map[string]interface{}),
		Metadata:  make(map[string]interface{}),
	}
	
	m.interactions = append(m.interactions, interaction)
	
	// Trim if exceeding max size
	if len(m.interactions) > m.maxSize {
		// Keep the most recent interactions
		m.interactions = m.interactions[len(m.interactions)-m.maxSize:]
	}
	
	// Save to disk
	m.save()
}

// GetRecentInteractions returns the most recent interactions
func (m *Manager) GetRecentInteractions(count int) []Interaction {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if count <= 0 || count > len(m.interactions) {
		count = len(m.interactions)
	}
	
	start := len(m.interactions) - count
	return m.interactions[start:]
}

// SearchInteractions searches for interactions containing the query
func (m *Manager) SearchInteractions(query string) []Interaction {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var results []Interaction
	queryLower := strings.ToLower(query)
	
	for _, interaction := range m.interactions {
		if strings.Contains(strings.ToLower(interaction.Input), queryLower) ||
		   strings.Contains(strings.ToLower(interaction.Output), queryLower) {
			results = append(results, interaction)
		}
	}
	
	return results
}

// GetSize returns the current number of interactions in memory
func (m *Manager) GetSize() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return len(m.interactions)
}

// Clear clears all interactions from memory
func (m *Manager) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.interactions = make([]Interaction, 0)
	m.save()
}

// GetContext generates context from recent interactions
func (m *Manager) GetContext(maxInteractions int) string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if maxInteractions <= 0 {
		maxInteractions = 5 // Default to last 5 interactions
	}
	
	recent := m.getRecentInteractions(maxInteractions)
	if len(recent) == 0 {
		return ""
	}
	
	var context strings.Builder
	context.WriteString("Recent conversation context:\n")
	
	for i, interaction := range recent {
		context.WriteString(fmt.Sprintf("\n%d. User: %s\n", i+1, interaction.Input))
		context.WriteString(fmt.Sprintf("   Assistant: %s\n", interaction.Output))
	}
	
	return context.String()
}

// GetSummary generates a summary of the conversation
func (m *Manager) GetSummary() string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if len(m.interactions) == 0 {
		return "No conversation history available."
	}
	
	totalInteractions := len(m.interactions)
	
	// Get time range
	oldest := m.interactions[0].Timestamp
	newest := m.interactions[totalInteractions-1].Timestamp
	
	// Count different types of interactions
	codeRequests := 0
	questions := 0
	commands := 0
	
	for _, interaction := range m.interactions {
		input := strings.ToLower(interaction.Input)
		if strings.Contains(input, "code") || strings.Contains(input, "function") || strings.Contains(input, "class") {
			codeRequests++
		} else if strings.HasPrefix(input, "/") {
			commands++
		} else {
			questions++
		}
	}
	
	return fmt.Sprintf(`Conversation Summary:
- Total interactions: %d
- Time range: %s to %s
- Code requests: %d
- Questions: %d  
- Commands: %d
- Duration: %s`,
		totalInteractions,
		oldest.Format("2006-01-02 15:04"),
		newest.Format("2006-01-02 15:04"),
		codeRequests,
		questions,
		commands,
		newest.Sub(oldest).Round(time.Minute))
}

// GetStats returns memory statistics
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_interactions": len(m.interactions),
		"max_size":          m.maxSize,
		"storage_file":      m.storageFile,
	}
	
	if len(m.interactions) > 0 {
		stats["oldest_interaction"] = m.interactions[0].Timestamp
		stats["newest_interaction"] = m.interactions[len(m.interactions)-1].Timestamp
	}
	
	return stats
}

// SetMaxSize sets the maximum number of interactions to keep
func (m *Manager) SetMaxSize(size int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.maxSize = size
	
	// Trim if current size exceeds new max
	if len(m.interactions) > m.maxSize {
		m.interactions = m.interactions[len(m.interactions)-m.maxSize:]
		m.save()
	}
}

// Export exports memory to a file
func (m *Manager) Export(filename string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	data, err := json.MarshalIndent(m.interactions, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal interactions: %w", err)
	}
	
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}
	
	return nil
}

// Import imports memory from a file
func (m *Manager) Import(filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read import file: %w", err)
	}
	
	var interactions []Interaction
	if err := json.Unmarshal(data, &interactions); err != nil {
		return fmt.Errorf("failed to unmarshal interactions: %w", err)
	}
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Append imported interactions
	m.interactions = append(m.interactions, interactions...)
	
	// Trim if exceeding max size
	if len(m.interactions) > m.maxSize {
		m.interactions = m.interactions[len(m.interactions)-m.maxSize:]
	}
	
	m.save()
	return nil
}

// Private methods

func (m *Manager) getRecentInteractions(count int) []Interaction {
	if count <= 0 || count > len(m.interactions) {
		count = len(m.interactions)
	}
	
	start := len(m.interactions) - count
	return m.interactions[start:]
}

func (m *Manager) save() {
	data, err := json.MarshalIndent(m.interactions, "", "  ")
	if err != nil {
		return // Silently fail for now
	}
	
	os.WriteFile(m.storageFile, data, 0600)
}

func (m *Manager) load() {
	data, err := os.ReadFile(m.storageFile)
	if err != nil {
		return // File doesn't exist or can't be read
	}
	
	var interactions []Interaction
	if err := json.Unmarshal(data, &interactions); err != nil {
		return // Invalid format
	}
	
	m.interactions = interactions
	
	// Trim if exceeding max size
	if len(m.interactions) > m.maxSize {
		m.interactions = m.interactions[len(m.interactions)-m.maxSize:]
		m.save() // Save the trimmed version
	}
}
