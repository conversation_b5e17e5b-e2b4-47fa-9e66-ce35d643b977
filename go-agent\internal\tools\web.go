package tools

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
)

// FetchWebpageTool fetches and parses web pages
type FetchWebpageTool struct{}

func (t *FetchWebpageTool) GetName() string        { return "fetch_webpage" }
func (t *FetchWebpageTool) GetDescription() string { return "Fetch and parse web page content" }
func (t *FetchWebpageTool) GetCategory() string    { return "web" }

func (t *FetchWebpageTool) IsRelevant(query string) bool {
	keywords := []string{"fetch", "webpage", "url", "scrape", "download"}
	return containsAny(query, keywords)
}

func (t *FetchWebpageTool) Execute(args map[string]interface{}) (interface{}, error) {
	urlStr, ok := args["url"].(string)
	if !ok {
		return nil, fmt.Errorf("url parameter is required")
	}

	// Validate URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Make request
	resp, err := client.Get(parsedURL.String())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch webpage: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error: %d %s", resp.StatusCode, resp.Status)
	}

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse HTML if requested
	parseHTML, _ := args["parse_html"].(bool)
	if parseHTML {
		doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
		if err != nil {
			return nil, fmt.Errorf("failed to parse HTML: %w", err)
		}

		// Extract useful information
		title := doc.Find("title").Text()
		description := doc.Find("meta[name='description']").AttrOr("content", "")
		
		// Extract text content
		textContent := doc.Find("body").Text()
		
		return map[string]interface{}{
			"url":         urlStr,
			"title":       title,
			"description": description,
			"content":     textContent,
			"html":        string(body),
			"status_code": resp.StatusCode,
		}, nil
	}

	return map[string]interface{}{
		"url":         urlStr,
		"content":     string(body),
		"status_code": resp.StatusCode,
		"headers":     resp.Header,
	}, nil
}

// WebSearchTool performs web searches
type WebSearchTool struct{}

func (t *WebSearchTool) GetName() string        { return "web_search" }
func (t *WebSearchTool) GetDescription() string { return "Search the web using various search engines" }
func (t *WebSearchTool) GetCategory() string    { return "web" }

func (t *WebSearchTool) IsRelevant(query string) bool {
	keywords := []string{"search", "web", "google", "find", "lookup"}
	return containsAny(query, keywords)
}

func (t *WebSearchTool) Execute(args map[string]interface{}) (interface{}, error) {
	query, ok := args["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query parameter is required")
	}

	engine, _ := args["engine"].(string)
	if engine == "" {
		engine = "duckduckgo" // Default to privacy-focused search
	}

	maxResults, _ := args["max_results"].(float64)
	if maxResults == 0 {
		maxResults = 10
	}

	// Perform search based on engine
	var results []SearchResult
	var err error

	switch engine {
	case "duckduckgo":
		results, err = t.searchDuckDuckGo(query, int(maxResults))
	case "bing":
		results, err = t.searchBing(query, int(maxResults))
	default:
		return nil, fmt.Errorf("unsupported search engine: %s", engine)
	}

	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	return map[string]interface{}{
		"query":   query,
		"engine":  engine,
		"results": results,
		"count":   len(results),
	}, nil
}

// GitHubSearchTool searches GitHub repositories
type GitHubSearchTool struct{}

func (t *GitHubSearchTool) GetName() string        { return "github_search" }
func (t *GitHubSearchTool) GetDescription() string { return "Search GitHub repositories and code" }
func (t *GitHubSearchTool) GetCategory() string    { return "web" }

func (t *GitHubSearchTool) IsRelevant(query string) bool {
	keywords := []string{"github", "repository", "repo", "code", "git"}
	return containsAny(query, keywords)
}

func (t *GitHubSearchTool) Execute(args map[string]interface{}) (interface{}, error) {
	query, ok := args["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query parameter is required")
	}

	searchType, _ := args["type"].(string)
	if searchType == "" {
		searchType = "repositories"
	}

	// GitHub API search
	apiURL := fmt.Sprintf("https://api.github.com/search/%s?q=%s", searchType, url.QueryEscape(query))

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("GitHub API request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GitHub API error: %d %s", resp.StatusCode, resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read GitHub API response: %w", err)
	}

	var searchResult GitHubSearchResult
	if err := json.Unmarshal(body, &searchResult); err != nil {
		return nil, fmt.Errorf("failed to parse GitHub API response: %w", err)
	}

	return map[string]interface{}{
		"query":        query,
		"type":         searchType,
		"total_count":  searchResult.TotalCount,
		"repositories": searchResult.Items,
	}, nil
}

// StackOverflowSearchTool searches Stack Overflow
type StackOverflowSearchTool struct{}

func (t *StackOverflowSearchTool) GetName() string        { return "stackoverflow_search" }
func (t *StackOverflowSearchTool) GetDescription() string { return "Search Stack Overflow for programming questions and answers" }
func (t *StackOverflowSearchTool) GetCategory() string    { return "web" }

func (t *StackOverflowSearchTool) IsRelevant(query string) bool {
	keywords := []string{"stackoverflow", "stack overflow", "programming", "question", "answer", "help"}
	return containsAny(query, keywords)
}

func (t *StackOverflowSearchTool) Execute(args map[string]interface{}) (interface{}, error) {
	query, ok := args["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query parameter is required")
	}

	// Stack Overflow API search
	apiURL := fmt.Sprintf("https://api.stackexchange.com/2.3/search/advanced?order=desc&sort=relevance&q=%s&site=stackoverflow", url.QueryEscape(query))

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("Stack Overflow API request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Stack Overflow API error: %d %s", resp.StatusCode, resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read Stack Overflow API response: %w", err)
	}

	var searchResult StackOverflowSearchResult
	if err := json.Unmarshal(body, &searchResult); err != nil {
		return nil, fmt.Errorf("failed to parse Stack Overflow API response: %w", err)
	}

	return map[string]interface{}{
		"query":     query,
		"questions": searchResult.Items,
		"has_more":  searchResult.HasMore,
	}, nil
}

// HTTPRequestTool makes custom HTTP requests
type HTTPRequestTool struct{}

func (t *HTTPRequestTool) GetName() string        { return "http_request" }
func (t *HTTPRequestTool) GetDescription() string { return "Make custom HTTP requests (GET, POST, PUT, DELETE)" }
func (t *HTTPRequestTool) GetCategory() string    { return "web" }

func (t *HTTPRequestTool) IsRelevant(query string) bool {
	keywords := []string{"http", "request", "api", "get", "post", "put", "delete", "curl"}
	return containsAny(query, keywords)
}

func (t *HTTPRequestTool) Execute(args map[string]interface{}) (interface{}, error) {
	urlStr, ok := args["url"].(string)
	if !ok {
		return nil, fmt.Errorf("url parameter is required")
	}

	method, _ := args["method"].(string)
	if method == "" {
		method = "GET"
	}

	// Prepare request
	var body strings.Reader
	if bodyData, ok := args["body"].(string); ok {
		body = *strings.NewReader(bodyData)
	}

	req, err := http.NewRequest(method, urlStr, &body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers
	if headers, ok := args["headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			if strValue, ok := value.(string); ok {
				req.Header.Set(key, strValue)
			}
		}
	}

	// Make request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return map[string]interface{}{
		"url":         urlStr,
		"method":      method,
		"status_code": resp.StatusCode,
		"status":      resp.Status,
		"headers":     resp.Header,
		"body":        string(respBody),
	}, nil
}

// Data structures for API responses

type SearchResult struct {
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Source      string `json:"source"`
}

type GitHubSearchResult struct {
	TotalCount int `json:"total_count"`
	Items      []struct {
		Name        string `json:"name"`
		FullName    string `json:"full_name"`
		Description string `json:"description"`
		HTMLURL     string `json:"html_url"`
		Language    string `json:"language"`
		Stars       int    `json:"stargazers_count"`
		Forks       int    `json:"forks_count"`
	} `json:"items"`
}

type StackOverflowSearchResult struct {
	Items   []StackOverflowQuestion `json:"items"`
	HasMore bool                    `json:"has_more"`
}

type StackOverflowQuestion struct {
	Title        string   `json:"title"`
	Link         string   `json:"link"`
	Score        int      `json:"score"`
	AnswerCount  int      `json:"answer_count"`
	Tags         []string `json:"tags"`
	IsAnswered   bool     `json:"is_answered"`
	CreationDate int64    `json:"creation_date"`
}

// Helper methods for search implementations

func (t *WebSearchTool) searchDuckDuckGo(query string, maxResults int) ([]SearchResult, error) {
	// Simplified DuckDuckGo search implementation
	// In a real implementation, you'd use their API or scrape results
	searchURL := fmt.Sprintf("https://duckduckgo.com/html/?q=%s", url.QueryEscape(query))
	
	fetchTool := &FetchWebpageTool{}
	_, err := fetchTool.Execute(map[string]interface{}{
		"url":        searchURL,
		"parse_html": true,
	})

	if err != nil {
		return nil, err
	}
	
	// Parse search results from HTML
	// This is a simplified implementation
	results := []SearchResult{
		{
			Title:       "Sample Result",
			URL:         "https://example.com",
			Description: "Sample search result description",
			Source:      "DuckDuckGo",
		},
	}
	
	return results, nil
}

func (t *WebSearchTool) searchBing(query string, maxResults int) ([]SearchResult, error) {
	// Simplified Bing search implementation
	// In a real implementation, you'd use Bing Search API
	return []SearchResult{
		{
			Title:       "Bing Result",
			URL:         "https://example.com",
			Description: "Sample Bing search result",
			Source:      "Bing",
		},
	}, nil
}
