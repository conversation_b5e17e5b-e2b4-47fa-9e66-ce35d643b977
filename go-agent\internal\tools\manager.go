package tools

import (
	"fmt"
	"strings"
	"sync"
)

// Tool interface defines the contract for all tools
type Tool interface {
	GetName() string
	GetDescription() string
	Execute(args map[string]interface{}) (interface{}, error)
	GetCategory() string
	IsRelevant(query string) bool
}

// Manager manages all available tools
type Manager struct {
	tools    map[string]Tool
	mu       sync.RWMutex
	registry *Registry
}

// Registry holds tool categories and metadata
type Registry struct {
	categories map[string][]Tool
	metadata   map[string]ToolMetadata
}

// ToolMetadata contains additional information about tools
type ToolMetadata struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Keywords    []string `json:"keywords"`
	Usage       string   `json:"usage"`
	Examples    []string `json:"examples"`
}

// NewManager creates a new tools manager
func NewManager() *Manager {
	manager := &Manager{
		tools: make(map[string]Tool),
		registry: &Registry{
			categories: make(map[string][]Tool),
			metadata:   make(map[string]ToolMetadata),
		},
	}

	// Register all tools
	manager.registerAllTools()

	return manager
}

// registerAllTools registers all available tools
func (m *Manager) registerAllTools() {
	// File System Tools
	m.registerTool(&CreateFileTool{})
	m.registerTool(&ReadFileTool{})
	m.registerTool(&WriteFileTool{})
	m.registerTool(&DeleteFileTool{})
	m.registerTool(&MoveFileTool{})
	m.registerTool(&CopyFileTool{})
	m.registerTool(&BackupFileTool{})
	m.registerTool(&ListDirectoryTool{})
	m.registerTool(&CreateDirectoryTool{})
	m.registerTool(&SearchFilesTool{})
	m.registerTool(&GrepSearchTool{})
	m.registerTool(&ReplaceInFileTool{})
	m.registerTool(&InsertIntoFileTool{})
	m.registerTool(&AppendToFileTool{})
	m.registerTool(&PrependToFileTool{})

	// Terminal Tools
	m.registerTool(&RunCommandTool{})
	m.registerTool(&GetTerminalOutputTool{})
	m.registerTool(&InstallPackagesTool{})
	m.registerTool(&ConfigureEnvironmentTool{})
	m.registerTool(&RunTestsTool{})
	m.registerTool(&LintCheckTool{})

	// Web Tools
	m.registerTool(&FetchWebpageTool{})
	m.registerTool(&WebSearchTool{})
	m.registerTool(&GitHubSearchTool{})
	m.registerTool(&StackOverflowSearchTool{})
	m.registerTool(&HTTPRequestTool{})

	// Code Analysis Tools
	m.registerTool(&AnalyzeCodeTool{})
	m.registerTool(&SecurityAuditTool{})
	m.registerTool(&RefactorCodeTool{})
	m.registerTool(&OptimizeCodeTool{})
	m.registerTool(&GenerateCodeTool{})

	// Ultra-Powerful Tools
	m.registerTool(&AutonomousCodingTool{})
	m.registerTool(&FullStackGeneratorTool{})
	m.registerTool(&UltraCodeAssistantTool{})
	m.registerTool(&AIAgentFusionTool{})
	m.registerTool(&IntelligentDebuggingTool{})

	// Note: Additional specialized tools can be added as needed
}

// registerTool registers a single tool
func (m *Manager) registerTool(tool Tool) {
	m.mu.Lock()
	defer m.mu.Unlock()

	name := tool.GetName()
	category := tool.GetCategory()

	m.tools[name] = tool

	// Add to category
	if m.registry.categories[category] == nil {
		m.registry.categories[category] = make([]Tool, 0)
	}
	m.registry.categories[category] = append(m.registry.categories[category], tool)

	// Add metadata
	m.registry.metadata[name] = ToolMetadata{
		Name:        name,
		Description: tool.GetDescription(),
		Category:    category,
		Keywords:    extractKeywords(tool.GetDescription()),
	}
}

// GetTool returns a tool by name
func (m *Manager) GetTool(name string) (Tool, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	tool, exists := m.tools[name]
	if !exists {
		return nil, fmt.Errorf("tool '%s' not found", name)
	}

	return tool, nil
}

// GetRelevantTools returns tools relevant to a query
func (m *Manager) GetRelevantTools(query string) []Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var relevantTools []Tool
	queryLower := strings.ToLower(query)

	for _, tool := range m.tools {
		if tool.IsRelevant(queryLower) {
			relevantTools = append(relevantTools, tool)
		}
	}

	// Limit to top 10 most relevant tools
	if len(relevantTools) > 10 {
		relevantTools = relevantTools[:10]
	}

	return relevantTools
}

// GetAllTools returns all registered tools
func (m *Manager) GetAllTools() map[string]Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent external modification
	tools := make(map[string]Tool)
	for name, tool := range m.tools {
		tools[name] = tool
	}

	return tools
}

// GetAllToolNames returns names of all tools
func (m *Manager) GetAllToolNames() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	names := make([]string, 0, len(m.tools))
	for name := range m.tools {
		names = append(names, name)
	}

	return names
}

// GetToolsByCategory returns tools in a specific category
func (m *Manager) GetToolsByCategory(category string) []Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.registry.categories[category]
}

// GetCategories returns all available categories
func (m *Manager) GetCategories() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	categories := make([]string, 0, len(m.registry.categories))
	for category := range m.registry.categories {
		categories = append(categories, category)
	}

	return categories
}

// GetToolCount returns the total number of registered tools
func (m *Manager) GetToolCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return len(m.tools)
}

// ExecuteTool executes a tool by name with given arguments
func (m *Manager) ExecuteTool(name string, args map[string]interface{}) (interface{}, error) {
	tool, err := m.GetTool(name)
	if err != nil {
		return nil, err
	}

	return tool.Execute(args)
}

// Helper functions

func extractKeywords(description string) []string {
	words := strings.Fields(strings.ToLower(description))
	keywords := make([]string, 0)

	for _, word := range words {
		// Remove punctuation and filter short words
		word = strings.Trim(word, ".,!?;:")
		if len(word) > 3 {
			keywords = append(keywords, word)
		}
	}

	return keywords
}
