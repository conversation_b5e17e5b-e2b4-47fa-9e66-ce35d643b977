package providers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"sync"
	"time"

	"go-agent/internal/config"
)

// GeminiProvider implements the Provider interface for Google Gemini
type GeminiProvider struct {
	config       *config.Config
	currentModel string
	usage        Usage
	mu           sync.RWMutex
	client       *http.Client
}

// GeminiRequest represents a request to Gemini API
type GeminiRequest struct {
	Contents         []GeminiContent `json:"contents"`
	GenerationConfig GeminiConfig    `json:"generationConfig"`
}

// GeminiContent represents content in Gemini format
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role,omitempty"`
}

// GeminiPart represents a part of content
type GeminiPart struct {
	Text string `json:"text"`
}

// GeminiConfig represents generation configuration
type GeminiConfig struct {
	Temperature     float64 `json:"temperature"`
	TopK            int     `json:"topK"`
	TopP            float64 `json:"topP"`
	MaxOutputTokens int     `json:"maxOutputTokens"`
}

// GeminiResponse represents Gemini API response
type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
	Usage      GeminiUsage       `json:"usageMetadata"`
}

// GeminiCandidate represents a response candidate
type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

// GeminiUsage represents usage statistics
type GeminiUsage struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// NewGeminiProvider creates a new Gemini provider
func NewGeminiProvider(cfg *config.Config) *GeminiProvider {
	return &GeminiProvider{
		config:       cfg,
		currentModel: "gemini-2.5-flash",
		usage:        Usage{},
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// GetName returns the provider name
func (p *GeminiProvider) GetName() string {
	return "gemini"
}

// IsConfigured checks if the provider is properly configured
func (p *GeminiProvider) IsConfigured() bool {
	return p.config.AI.Gemini.APIKey != ""
}

// GenerateResponse generates a response using Gemini
func (p *GeminiProvider) GenerateResponse(aiContext AIContext) (string, error) {
	if !p.IsConfigured() {
		return "", fmt.Errorf("Gemini provider not configured")
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	startTime := time.Now()

	// Convert messages to Gemini format
	contents := make([]GeminiContent, 0)
	for _, msg := range aiContext.Messages {
		if msg.Role == "system" {
			// Gemini doesn't have system role, prepend to first user message
			continue
		}
		
		role := msg.Role
		if role == "assistant" {
			role = "model"
		}
		
		content := GeminiContent{
			Parts: []GeminiPart{{Text: msg.Content}},
			Role:  role,
		}
		contents = append(contents, content)
	}

	// Prepare request
	req := GeminiRequest{
		Contents: contents,
		GenerationConfig: GeminiConfig{
			Temperature:     aiContext.Temperature,
			TopK:            40,
			TopP:            0.95,
			MaxOutputTokens: aiContext.MaxTokens,
		},
	}

	// Make API call
	response, err := p.makeAPICall(req)
	if err != nil {
		p.usage.ErrorCount++
		return "", fmt.Errorf("Gemini API error: %w", err)
	}

	// Update usage statistics
	p.updateUsage(startTime, response.Usage)

	if len(response.Candidates) == 0 {
		return "", fmt.Errorf("no response from Gemini")
	}

	if len(response.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("empty response from Gemini")
	}

	return response.Candidates[0].Content.Parts[0].Text, nil
}

// GetModels returns available models
func (p *GeminiProvider) GetModels() []string {
	return []string{
	
		"gemini-1.5-flash",
		"gemini-2.0-flash",
		"gemini-2.5-flash",
	
	}
}

// SetModel sets the current model
func (p *GeminiProvider) SetModel(model string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Validate model
	validModels := p.GetModels()
	for _, validModel := range validModels {
		if model == validModel {
			p.currentModel = model
			return nil
		}
	}

	return fmt.Errorf("invalid model: %s", model)
}

// GetUsage returns usage statistics
func (p *GeminiProvider) GetUsage() Usage {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return p.usage
}

// HealthCheck performs a health check
func (p *GeminiProvider) HealthCheck() error {
	if !p.IsConfigured() {
		return fmt.Errorf("provider not configured")
	}

	// Simple health check with a minimal request
	req := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{{Text: "Hello"}},
				Role:  "user",
			},
		},
		GenerationConfig: GeminiConfig{
			Temperature:     0.1,
			MaxOutputTokens: 5,
		},
	}

	_, err := p.makeAPICall(req)
	return err
}

// makeAPICall makes a request to the Gemini API
func (p *GeminiProvider) makeAPICall(req GeminiRequest) (*GeminiResponse, error) {
	// Prepare URL
	url := fmt.Sprintf("https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s",
		p.currentModel, p.config.AI.Gemini.APIKey)

	// Marshal request
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &geminiResp, nil
}

// updateUsage updates usage statistics
func (p *GeminiProvider) updateUsage(startTime time.Time, usage GeminiUsage) {
	duration := time.Since(startTime)

	p.usage.RequestCount++
	p.usage.TokensUsed += int64(usage.TotalTokenCount)
	p.usage.LastUsed = time.Now()

	// Update average latency
	if p.usage.RequestCount == 1 {
		p.usage.AverageLatency = duration
	} else {
		// Calculate running average
		totalLatency := p.usage.AverageLatency * time.Duration(p.usage.RequestCount-1)
		p.usage.AverageLatency = (totalLatency + duration) / time.Duration(p.usage.RequestCount)
	}
}

// GetCurrentModel returns the current model
func (p *GeminiProvider) GetCurrentModel() string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.currentModel
}

// GetTokenLimit returns the token limit for the current model
func (p *GeminiProvider) GetTokenLimit() int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	switch p.currentModel {
	case "gemini-2.5-flash":
		return 2097152 // 2M tokens
	case "gemini-2.0-flash":
		return 1048576 // 1M tokens
	case "gemini-1.5-flash":
		return 32768 // 32K tokens
	default:
		return 32768
	}
}

// EstimateTokens estimates the number of tokens in a text
func (p *GeminiProvider) EstimateTokens(text string) int {
	// Rough estimation: ~4 characters per token for English
	return len(text) / 4
}

// CanHandleRequest checks if the request can be handled within token limits
func (p *GeminiProvider) CanHandleRequest(aiContext AIContext) bool {
	totalTokens := 0

	// Estimate tokens in messages
	for _, msg := range aiContext.Messages {
		totalTokens += p.EstimateTokens(msg.Content)
	}

	// Add buffer for response
	totalTokens += aiContext.MaxTokens

	return totalTokens <= p.GetTokenLimit()
}

// OptimizeRequest optimizes the request to fit within token limits
func (p *GeminiProvider) OptimizeRequest(aiContext AIContext) AIContext {
	if p.CanHandleRequest(aiContext) {
		return aiContext
	}

	// Simple optimization: truncate messages if needed
	optimized := aiContext
	tokenLimit := p.GetTokenLimit() - aiContext.MaxTokens - 1000 // Buffer

	currentTokens := 0
	optimizedMessages := make([]Message, 0)

	// Keep system message if present
	if len(aiContext.Messages) > 0 && aiContext.Messages[0].Role == "system" {
		optimizedMessages = append(optimizedMessages, aiContext.Messages[0])
		currentTokens += p.EstimateTokens(aiContext.Messages[0].Content)
	}

	// Add messages from the end (most recent first)
	for i := len(aiContext.Messages) - 1; i >= 0; i-- {
		msg := aiContext.Messages[i]
		if msg.Role == "system" {
			continue // Already added
		}

		msgTokens := p.EstimateTokens(msg.Content)
		if currentTokens+msgTokens <= tokenLimit {
			optimizedMessages = append([]Message{msg}, optimizedMessages...)
			currentTokens += msgTokens
		} else {
			break
		}
	}

	optimized.Messages = optimizedMessages
	return optimized
}

// GetPricing returns pricing information for the current model
func (p *GeminiProvider) GetPricing() map[string]float64 {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// Pricing per 1M tokens (as of 2024)
	pricing := map[string]map[string]float64{
		"gemini-2.5-flash":     {"input": 3.5, "output": 10.5},
		"gemini-2.0-flash":   {"input": 0.075, "output": 0.3},
		"gemini-1.5-flash":   {"input": 0.075, "output": 0.3},
	
	}

	if modelPricing, exists := pricing[p.currentModel]; exists {
		return modelPricing
	}

	return map[string]float64{"input": 0.5, "output": 1.5} // Default
}

// EstimateCost estimates the cost of a request
func (p *GeminiProvider) EstimateCost(aiContext AIContext) float64 {
	pricing := p.GetPricing()

	inputTokens := 0
	for _, msg := range aiContext.Messages {
		inputTokens += p.EstimateTokens(msg.Content)
	}

	outputTokens := aiContext.MaxTokens

	inputCost := float64(inputTokens) / 1000000.0 * pricing["input"]
	outputCost := float64(outputTokens) / 1000000.0 * pricing["output"]

	return inputCost + outputCost
}
