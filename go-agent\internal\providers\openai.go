package providers

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"
	"go-agent/internal/config"
)

// OpenAIProvider implements the Provider interface for OpenAI
type OpenAIProvider struct {
	client      *openai.Client
	config      *config.Config
	currentModel string
	usage       Usage
	mu          sync.RWMutex
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(cfg *config.Config) *OpenAIProvider {
	provider := &OpenAIProvider{
		config:       cfg,
		currentModel: "gpt-4-turbo-preview",
		usage:        Usage{},
	}

	if cfg.AI.OpenAI.APIKey != "" {
		provider.client = openai.NewClient(cfg.AI.OpenAI.APIKey)
	}

	return provider
}

// GetName returns the provider name
func (p *OpenAIProvider) GetName() string {
	return "openai"
}

// IsConfigured checks if the provider is properly configured
func (p *OpenAIProvider) IsConfigured() bool {
	return p.client != nil && p.config.AI.OpenAI.APIKey != ""
}

// GenerateResponse generates a response using OpenAI
func (p *OpenAIProvider) GenerateResponse(aiContext AIContext) (string, error) {
	if !p.IsConfigured() {
		return "", fmt.Errorf("OpenAI provider not configured")
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	startTime := time.Now()

	// Convert messages to OpenAI format
	messages := make([]openai.ChatCompletionMessage, len(aiContext.Messages))
	for i, msg := range aiContext.Messages {
		messages[i] = openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
			Name:    msg.Name,
		}
	}

	// Prepare request
	req := openai.ChatCompletionRequest{
		Model:       p.currentModel,
		Messages:    messages,
		MaxTokens:   aiContext.MaxTokens,
		Temperature: float32(aiContext.Temperature),
	}

	// Add tools if provided
	if len(aiContext.Tools) > 0 {
		tools := make([]openai.Tool, len(aiContext.Tools))
		for i, tool := range aiContext.Tools {
			tools[i] = openai.Tool{
				Type: openai.ToolTypeFunction,
				Function: openai.FunctionDefinition{
					Name:        tool.Name,
					Description: tool.Description,
					Parameters:  tool.Parameters,
				},
			}
		}
		req.Tools = tools
	}

	// Make API call
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	resp, err := p.client.CreateChatCompletion(ctx, req)
	if err != nil {
		p.usage.ErrorCount++
		return "", fmt.Errorf("OpenAI API error: %w", err)
	}

	// Update usage statistics
	p.updateUsage(startTime, resp.Usage)

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("no response from OpenAI")
	}

	return resp.Choices[0].Message.Content, nil
}

// GetModels returns available models
func (p *OpenAIProvider) GetModels() []string {
	return []string{
		"gpt-4-turbo-preview",
		"gpt-4",
		"gpt-4-32k",
		"gpt-3.5-turbo",
		"gpt-3.5-turbo-16k",
		"gpt-4o",
		"gpt-4o-mini",
	}
}

// SetModel sets the current model
func (p *OpenAIProvider) SetModel(model string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Validate model
	validModels := p.GetModels()
	for _, validModel := range validModels {
		if model == validModel {
			p.currentModel = model
			return nil
		}
	}

	return fmt.Errorf("invalid model: %s", model)
}

// GetUsage returns usage statistics
func (p *OpenAIProvider) GetUsage() Usage {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return p.usage
}

// HealthCheck performs a health check
func (p *OpenAIProvider) HealthCheck() error {
	if !p.IsConfigured() {
		return fmt.Errorf("provider not configured")
	}

	// Simple health check with a minimal request
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req := openai.ChatCompletionRequest{
		Model: "gpt-3.5-turbo",
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "user",
				Content: "Hello",
			},
		},
		MaxTokens: 5,
	}

	_, err := p.client.CreateChatCompletion(ctx, req)
	return err
}

// updateUsage updates usage statistics
func (p *OpenAIProvider) updateUsage(startTime time.Time, usage openai.Usage) {
	duration := time.Since(startTime)
	
	p.usage.RequestCount++
	p.usage.TokensUsed += int64(usage.TotalTokens)
	p.usage.LastUsed = time.Now()
	
	// Update average latency
	if p.usage.RequestCount == 1 {
		p.usage.AverageLatency = duration
	} else {
		// Calculate running average
		totalLatency := p.usage.AverageLatency * time.Duration(p.usage.RequestCount-1)
		p.usage.AverageLatency = (totalLatency + duration) / time.Duration(p.usage.RequestCount)
	}
}

// GetCurrentModel returns the current model
func (p *OpenAIProvider) GetCurrentModel() string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.currentModel
}

// GetTokenLimit returns the token limit for the current model
func (p *OpenAIProvider) GetTokenLimit() int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	switch p.currentModel {
	case "gpt-4-turbo-preview":
		return 128000
	case "gpt-4":
		return 8192
	case "gpt-4-32k":
		return 32768
	case "gpt-3.5-turbo":
		return 4096
	case "gpt-3.5-turbo-16k":
		return 16384
	case "gpt-4o":
		return 128000
	case "gpt-4o-mini":
		return 128000
	default:
		return 4096
	}
}

// EstimateTokens estimates the number of tokens in a text
func (p *OpenAIProvider) EstimateTokens(text string) int {
	// Rough estimation: ~4 characters per token
	return len(text) / 4
}

// CanHandleRequest checks if the request can be handled within token limits
func (p *OpenAIProvider) CanHandleRequest(aiContext AIContext) bool {
	totalTokens := 0
	
	// Estimate tokens in messages
	for _, msg := range aiContext.Messages {
		totalTokens += p.EstimateTokens(msg.Content)
	}
	
	// Add buffer for response
	totalTokens += aiContext.MaxTokens
	
	return totalTokens <= p.GetTokenLimit()
}

// OptimizeRequest optimizes the request to fit within token limits
func (p *OpenAIProvider) OptimizeRequest(aiContext AIContext) AIContext {
	if p.CanHandleRequest(aiContext) {
		return aiContext
	}
	
	// Simple optimization: truncate messages if needed
	optimized := aiContext
	tokenLimit := p.GetTokenLimit() - aiContext.MaxTokens - 1000 // Buffer
	
	currentTokens := 0
	optimizedMessages := make([]Message, 0)
	
	// Keep system message if present
	if len(aiContext.Messages) > 0 && aiContext.Messages[0].Role == "system" {
		optimizedMessages = append(optimizedMessages, aiContext.Messages[0])
		currentTokens += p.EstimateTokens(aiContext.Messages[0].Content)
	}
	
	// Add messages from the end (most recent first)
	for i := len(aiContext.Messages) - 1; i >= 0; i-- {
		msg := aiContext.Messages[i]
		if msg.Role == "system" {
			continue // Already added
		}
		
		msgTokens := p.EstimateTokens(msg.Content)
		if currentTokens + msgTokens <= tokenLimit {
			optimizedMessages = append([]Message{msg}, optimizedMessages...)
			currentTokens += msgTokens
		} else {
			break
		}
	}
	
	optimized.Messages = optimizedMessages
	return optimized
}

// GetPricing returns pricing information for the current model
func (p *OpenAIProvider) GetPricing() map[string]float64 {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// Pricing per 1K tokens (as of 2024)
	pricing := map[string]map[string]float64{
		"gpt-4-turbo-preview": {"input": 0.01, "output": 0.03},
		"gpt-4":               {"input": 0.03, "output": 0.06},
		"gpt-4-32k":           {"input": 0.06, "output": 0.12},
		"gpt-3.5-turbo":       {"input": 0.0015, "output": 0.002},
		"gpt-3.5-turbo-16k":   {"input": 0.003, "output": 0.004},
		"gpt-4o":              {"input": 0.005, "output": 0.015},
		"gpt-4o-mini":         {"input": 0.00015, "output": 0.0006},
	}

	if modelPricing, exists := pricing[p.currentModel]; exists {
		return modelPricing
	}

	return map[string]float64{"input": 0.001, "output": 0.002} // Default
}

// EstimateCost estimates the cost of a request
func (p *OpenAIProvider) EstimateCost(aiContext AIContext) float64 {
	pricing := p.GetPricing()
	
	inputTokens := 0
	for _, msg := range aiContext.Messages {
		inputTokens += p.EstimateTokens(msg.Content)
	}
	
	outputTokens := aiContext.MaxTokens
	
	inputCost := float64(inputTokens) / 1000.0 * pricing["input"]
	outputCost := float64(outputTokens) / 1000.0 * pricing["output"]
	
	return inputCost + outputCost
}
