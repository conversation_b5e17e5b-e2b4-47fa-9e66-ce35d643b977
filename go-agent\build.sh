#!/bin/bash

# OpenCode AI Terminal Agent - Go Edition Build Script
# This script builds the Go agent with all dependencies

set -e

echo "🚀 Building OpenCode AI Terminal Agent - Go Edition"
echo "=================================================="

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

# Check Go version
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ Go version: $GO_VERSION"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -f go-agent go-agent.exe

# Download dependencies
echo "📦 Downloading dependencies..."
go mod tidy

# Run tests
echo "🧪 Running tests..."
go test ./... -v

# Build for current platform
echo "🔨 Building for current platform..."
go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent main.go

# Make executable (Unix systems)
if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    chmod +x go-agent
fi

# Build for multiple platforms
echo "🌍 Building for multiple platforms..."

# Windows
echo "  📦 Building for Windows..."
GOOS=windows GOARCH=amd64 go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent-windows-amd64.exe main.go

# Linux
echo "  🐧 Building for Linux..."
GOOS=linux GOARCH=amd64 go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent-linux-amd64 main.go

# macOS
echo "  🍎 Building for macOS..."
GOOS=darwin GOARCH=amd64 go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent-darwin-amd64 main.go
GOOS=darwin GOARCH=arm64 go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent-darwin-arm64 main.go

# ARM Linux (Raspberry Pi, etc.)
echo "  🔧 Building for ARM Linux..."
GOOS=linux GOARCH=arm64 go build -ldflags "-X main.version=2.0.0 -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o go-agent-linux-arm64 main.go

echo ""
echo "✅ Build completed successfully!"
echo ""
echo "📦 Generated binaries:"
ls -la go-agent* | grep -E "(go-agent|\.exe)$"

echo ""
echo "🚀 To run the agent:"
echo "  ./go-agent                    # Current platform"
echo "  ./go-agent config             # Configure API keys"
echo "  ./go-agent test               # Run system tests"
echo "  ./go-agent --help             # Show help"

echo ""
echo "🎉 OpenCode AI Terminal Agent - Go Edition is ready!"
echo "   Ultra-powerful AI coding assistant with 154+ tools"
echo "   Built with Go for maximum performance and reliability"
