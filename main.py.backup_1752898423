|
"""
This module provides a function to respond to user greetings with a friendly message.
It includes error handling and follows best practices for Python code.
"""

def greet_user(user_input: str) -> str:
    """
    Responds to the user's greeting with a friendly message.

    Args:
        user_input: The user's input string.

    Returns:
        A friendly greeting message if the user input is a greeting,
        otherwise, a default response.

    Raises:
        TypeError: If the input is not a string.
        ValueError: If the input string is empty.
    """

    if not isinstance(user_input, str):
        raise TypeError("Input must be a string.")

    if not user_input:
        raise ValueError("Input string cannot be empty.")

    # Convert the input to lowercase for case-insensitive matching
    user_input_lower = user_input.lower()

    # Check for common greetings
    if any(greeting in user_input_lower for greeting in ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"]):
        return "Hello there! How can I help you today?"
    else:
        return "I'm here to assist you. Please let me know what you need."


def main():
    """
    Main function to demonstrate the greet_user function.
    """
    try:
        user_input = input("Enter your greeting: ")
        response = greet_user(user_input)
        print(response)

    except TypeError as e:
        print(f"Error: {e}")
    except ValueError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()
```